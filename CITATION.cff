cff-version: 1.2.0
message: "If you use this software, please cite it using the following reference:"
title: "MuJoCo Playground: An open-source framework for GPU-accelerated robot learning and sim-to-real transfer."
authors:
  - family-names: <PERSON><PERSON><PERSON>
    given-names: Kevin
  - family-names: <PERSON><PERSON><PERSON>ur
    given-names: Baruch
  - family-names: <PERSON>o
    given-names: <PERSON><PERSON><PERSON>
  - family-names: <PERSON><PERSON><PERSON><PERSON>
    given-names: Mustafa
  - family-names: Holt
    given-names: Samuel
  - family-names: <PERSON>o
    given-names: <PERSON>
  - family-names: Allshire
    given-names: Arthur
  - family-names: Frey
    given-names: Erik
  - family-names: Sreenath
    given-names: <PERSON><PERSON><PERSON>
  - family-names: Kahrs
    given-names: <PERSON><PERSON><PERSON> <PERSON>.
  - family-names: Sferrazza
    given-names: Carlo
  - family-names: <PERSON>ssa
    given-names: Yuval
  - family-names: <PERSON><PERSON><PERSON>
    given-names: Pieter
year: 2025
repository-code: "https://github.com/google-deepmind/mujoco_playground"
url: "https://github.com/google-deepmind/mujoco_playground"
license: "Apache-2.0"
