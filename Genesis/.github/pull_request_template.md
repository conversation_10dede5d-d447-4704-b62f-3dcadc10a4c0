<!--  Thanks for sending a pull request!  Please:

1. Follow our contributor guidelines: 
   https://github.com/Genesis-Embodied-AI/Genesis/blob/main/.github/CONTRIBUTING.md
2. Prepare your PR according to the "Submitting Code Changes" 
   section of https://github.com/Genesis-Embodied-AI/Genesis/blob/main/.github/CONTRIBUTING.md
3. Provide a concise summary of your changes in the Title above
4. Prefix the title according to the type of issue you are addressing. Use:
    - [BUG FIX] for non-breaking changes which fix an issue
    - [FEATURE] for non-breaking changes which add functionality
    - [MISC] for minor changes such as improved inline documentation or fixing typos
    - [BREAKING] **in addition to the above** for breaking changes, i.e., a fix or feature that would cause existing APIs or functionality to change
-->

## Description
<!--- Describe your changes in detail -->

## Related Issue
<!--- This project only accepts pull requests related to open issues.
If suggesting a new feature or change, please discuss it in an issue first.
If fixing a bug, there should be an issue describing it with steps to reproduce.

Please link to the relevant issue here, e.g. via `Resolves <issue-url>`.
Cf. https://docs.github.com/en/issues/tracking-your-work-with-issues/using-issues/linking-a-pull-request-to-an-issue -->

Resolves Genesis-Embodied-AI/Genesis#<your-issue-number>

## Motivation and Context
<!--- Why is this change required? What problem does it solve? -->

## How Has This Been / Can This Be Tested?
<!--- Please describe in detail how you tested your changes.
Include details of your testing environment, and the tests you ran to
see how your change affects other areas of the code, etc. -->

## Screenshots (if appropriate):

## Checklist:
<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->
- [ ] I read the **CONTRIBUTING** document.
- [ ] I followed the `Submitting Code Changes` section of **CONTRIBUTING** document.
- [ ] I tagged the title correctly (including BUG FIX/FEATURE/MISC/BREAKING)
- [ ] I updated the [documentation](https://github.com/Genesis-Embodied-AI/genesis-doc) accordingly or no change is needed.
- [ ] I tested my changes and added instructions on how to test it for reviewers.

<!--- Optionally -->
- [ ] I have added tests to cover my changes.
- [ ] All new and existing tests passed.
