import argparse
import numpy as np
import genesis as gs
import time
import threading
import sys
import select
import termios
import tty

# Try to import pynput, but provide fallback if it fails
try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("Warning: pynput not available, using terminal input mode")


class DroneController:
    def __init__(self, use_terminal_input=False):
        self.thrust = 14468.429183500699  # Base hover RPM - constant hover
        self.rotation_delta = 200  # Differential RPM for rotation
        self.thrust_delta = 10  # Amount to change thrust by when accelerating/decelerating
        self.running = True
        self.rpms = [self.thrust] * 4
        self.pressed_keys = set()
        self.use_terminal_input = use_terminal_input

        # Terminal input setup
        if self.use_terminal_input:
            self.old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())

    def cleanup_terminal(self):
        if self.use_terminal_input:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)

    def on_press(self, key):
        try:
            if hasattr(keyboard, 'Key') and key == keyboard.Key.esc:
                self.running = False
                return False
            self.pressed_keys.add(key)
            print(f"Key pressed: {key}")
        except AttributeError:
            pass

    def on_release(self, key):
        try:
            self.pressed_keys.discard(key)
        except KeyError:
            pass

    def check_terminal_input(self):
        """Check for terminal input without blocking"""
        if not self.use_terminal_input:
            return

        if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
            char = sys.stdin.read(1)
            if char:
                # Map characters to keys
                if char == '\x1b':  # ESC sequence
                    # Read the rest of the escape sequence
                    char2 = sys.stdin.read(1)
                    if char2 == '[':
                        char3 = sys.stdin.read(1)
                        if char3 == 'A':  # Up arrow
                            self.pressed_keys.add('up')
                        elif char3 == 'B':  # Down arrow
                            self.pressed_keys.add('down')
                        elif char3 == 'C':  # Right arrow
                            self.pressed_keys.add('right')
                        elif char3 == 'D':  # Left arrow
                            self.pressed_keys.add('left')
                    else:
                        # Just ESC key
                        self.running = False
                elif char == ' ':  # Space
                    self.pressed_keys.add('space')
                elif char == 'q' or char == 'Q':  # Quit
                    self.running = False
                elif char == 'w' or char == 'W':  # Forward
                    self.pressed_keys.add('up')
                elif char == 's' or char == 'S':  # Backward
                    self.pressed_keys.add('down')
                elif char == 'a' or char == 'A':  # Left
                    self.pressed_keys.add('left')
                elif char == 'd' or char == 'D':  # Right
                    self.pressed_keys.add('right')
                elif char == 'e' or char == 'E':  # Up/Accelerate
                    self.pressed_keys.add('space')
                elif char == 'c' or char == 'C':  # Down/Decelerate
                    self.pressed_keys.add('shift')

                # Clear keys after a short time to simulate key release
                threading.Timer(0.1, lambda: self.pressed_keys.clear()).start()

    def update_thrust(self):
        # Check for terminal input if using that mode
        if self.use_terminal_input:
            self.check_terminal_input()

        # Store previous RPMs for debugging
        prev_rpms = self.rpms.copy()

        # Reset RPMs to hover thrust
        self.rpms = [self.thrust] * 4

        # Define key checks that work for both input methods
        space_pressed = False
        shift_pressed = False
        up_pressed = False
        down_pressed = False
        left_pressed = False
        right_pressed = False

        if self.use_terminal_input:
            space_pressed = 'space' in self.pressed_keys
            shift_pressed = 'shift' in self.pressed_keys
            up_pressed = 'up' in self.pressed_keys
            down_pressed = 'down' in self.pressed_keys
            left_pressed = 'left' in self.pressed_keys
            right_pressed = 'right' in self.pressed_keys
        else:
            if PYNPUT_AVAILABLE:
                space_pressed = keyboard.Key.space in self.pressed_keys
                shift_pressed = keyboard.Key.shift in self.pressed_keys
                up_pressed = keyboard.Key.up in self.pressed_keys
                down_pressed = keyboard.Key.down in self.pressed_keys
                left_pressed = keyboard.Key.left in self.pressed_keys
                right_pressed = keyboard.Key.right in self.pressed_keys

        # Acceleration (Spacebar) - All rotors spin faster
        if space_pressed:
            self.thrust += self.thrust_delta
            self.rpms = [self.thrust] * 4
            print("Accelerating")

        # Deceleration (Left Shift) - All rotors spin slower
        if shift_pressed:
            self.thrust -= self.thrust_delta
            self.rpms = [self.thrust] * 4
            print("Decelerating")

        # Forward (North) - Front rotors spin faster
        if up_pressed:
            self.rpms[0] += self.rotation_delta  # Front left
            self.rpms[1] += self.rotation_delta  # Front right
            self.rpms[2] -= self.rotation_delta  # Back left
            self.rpms[3] -= self.rotation_delta  # Back right
            print("Moving Forward")

        # Backward (South) - Back rotors spin faster
        if down_pressed:
            self.rpms[0] -= self.rotation_delta  # Front left
            self.rpms[1] -= self.rotation_delta  # Front right
            self.rpms[2] += self.rotation_delta  # Back left
            self.rpms[3] += self.rotation_delta  # Back right
            print("Moving Backward")

        # Left (West) - Left rotors spin faster
        if left_pressed:
            self.rpms[0] -= self.rotation_delta  # Front left
            self.rpms[2] -= self.rotation_delta  # Back left
            self.rpms[1] += self.rotation_delta  # Front right
            self.rpms[3] += self.rotation_delta  # Back right
            print("Moving Left")

        # Right (East) - Right rotors spin faster
        if right_pressed:
            self.rpms[0] += self.rotation_delta  # Front left
            self.rpms[2] += self.rotation_delta  # Back left
            self.rpms[1] -= self.rotation_delta  # Front right
            self.rpms[3] -= self.rotation_delta  # Back right
            print("Moving Right")

        self.rpms = np.clip(self.rpms, 0, 25000)

        # Debug print if any RPMs changed
        if not np.array_equal(prev_rpms, self.rpms):
            print(f"RPMs changed from {prev_rpms} to {self.rpms}")

        return self.rpms


def run_sim(scene, drone, controller):
    try:
        while controller.running:
            try:
                # Update drone with current RPMs
                rpms = controller.update_thrust()
                drone.set_propellels_rpm(rpms)

                # Update physics
                scene.step()

                time.sleep(1 / 60)  # Limit simulation rate
            except Exception as e:
                print(f"Error in simulation loop: {e}")
                break
    finally:
        # Clean up terminal settings if needed
        controller.cleanup_terminal()
        if scene.viewer:
            scene.viewer.stop()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-v", "--vis", action="store_true", default=True, help="Enable visualization (default: True)")
    parser.add_argument("-m", "--mac", action="store_true", default=False, help="Running on MacOS (default: False)")
    parser.add_argument("-t", "--terminal", action="store_true", default=False, help="Use terminal input instead of global keyboard monitoring (default: False)")
    args = parser.parse_args()

    # Initialize Genesis
    gs.init(backend=gs.cpu)

    # Create scene with initial camera view
    viewer_options = gs.options.ViewerOptions(
        camera_pos=(0.0, -4.0, 2.0),  # Now behind the drone (negative Y)
        camera_lookat=(0.0, 0.0, 0.5),
        camera_fov=45,
        max_FPS=60,
    )

    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=0.01,
            gravity=(0, 0, -9.81),
        ),
        viewer_options=viewer_options,
        show_viewer=args.vis,
    )

    # Add entities
    plane = scene.add_entity(gs.morphs.Plane())
    drone = scene.add_entity(
        morph=gs.morphs.Drone(
            file="urdf/drones/cf2x.urdf",
            pos=(0.0, 0, 0.5),  # Start a bit higher
        ),
    )

    scene.viewer.follow_entity(drone)

    # Build scene
    scene.build()

    # Determine input method
    use_terminal_input = args.terminal or not PYNPUT_AVAILABLE

    # Initialize controller
    controller = DroneController(use_terminal_input=use_terminal_input)

    # Print control instructions
    print("\nDrone Controls:")
    if use_terminal_input:
        print("Terminal Input Mode:")
        print("W/↑ - Move Forward (North)")
        print("S/↓ - Move Backward (South)")
        print("A/← - Move Left (West)")
        print("D/→ - Move Right (East)")
        print("E/Space - Increase Thrust (Accelerate)")
        print("C - Decrease Thrust (Decelerate)")
        print("Q/ESC - Quit")
        print("\nNote: Press keys and they will be detected immediately")
    else:
        print("Global Keyboard Mode:")
        print("↑ - Move Forward (North)")
        print("↓ - Move Backward (South)")
        print("← - Move Left (West)")
        print("→ - Move Right (East)")
        print("Space - Increase Thrust (Accelerate)")
        print("Left Shift - Decrease Thrust (Decelerate)")
        print("ESC - Quit")
    print(f"\nInitial hover RPM: {controller.thrust}")

    # Start keyboard listener only if not using terminal input
    listener = None
    if not use_terminal_input and PYNPUT_AVAILABLE:
        try:
            listener = keyboard.Listener(on_press=controller.on_press, on_release=controller.on_release)
            listener.start()
            print("Global keyboard listener started successfully")
        except Exception as e:
            print(f"Failed to start global keyboard listener: {e}")
            print("Falling back to terminal input mode")
            controller.use_terminal_input = True
            use_terminal_input = True

    try:
        if args.mac:
            # Run simulation in another thread
            sim_thread = threading.Thread(target=run_sim, args=(scene, drone, controller))
            sim_thread.start()

            if args.vis:
                scene.viewer.start()

            # Wait for threads to finish
            sim_thread.join()
        else:
            # Run simulation in main thread
            run_sim(scene, drone, controller)
    finally:
        if listener:
            listener.stop()


if __name__ == "__main__":
    main()
