<?xml version="1.0" ?>
<robot name="partnet_6b8b2cb01c376064c8724d5673a063a6">
	<link name="base"/>
	<link name="link_0">
		<visual name="lid-7">
			<origin xyz="0.005474442099535147 0 0.001057485751011103"/>
			<geometry>
				<mesh filename="textured_objs/original-2.obj"/>
			</geometry>
		</visual>
		<collision>
			<origin xyz="0.005474442099535147 0 0.001057485751011103"/>
			<geometry>
				<mesh filename="textured_objs/original-2_vhacd.obj"/>
			</geometry>
		</collision>
	</link>
	<joint name="joint_0" type="continuous">
		<origin xyz="0.0 0 0.0"/>
		<axis xyz="0 1 0"/>
		<child link="link_0"/>
		<parent link="link_0_helper"/>
	</joint>
	<link name="link_1">
		<visual name="body-5">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-3.obj"/>
			</geometry>
		</visual>
		<visual name="neck-6">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-1.obj"/>
			</geometry>
		</visual>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-3_vhacd.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-1_vhacd.obj"/>
			</geometry>
		</collision>
	</link>
	<joint name="joint_1" type="fixed">
		<origin rpy="1.570796326794897 0 -1.570796326794897" xyz="0 0 0"/>
		<child link="link_1"/>
		<parent link="base"/>
	</joint>
	<link name="link_0_helper"/>
	<joint name="joint_2" type="prismatic">
		<origin xyz="-0.005474442099535147 0 -0.001057485751011103"/>
		<axis xyz="0 1 0"/>
		<child link="link_0_helper"/>
		<parent link="link_1"/>
		<limit lower="-0.1080000000000001" upper="0.208000000000000007"/>
	</joint>
</robot>
