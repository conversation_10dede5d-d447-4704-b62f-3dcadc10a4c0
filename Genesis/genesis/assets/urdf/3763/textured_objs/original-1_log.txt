V-HACD V2.2
Syntax: testVHACD [options] --input infile.obj --output outfile.obj --log logfile.txt

Options:
       --input                     Wavefront .obj input file name
       --output                    VRML 2.0 output file name
       --log                       Log file name
       --resolution                Maximum number of voxels generated during the voxelization stage (default=100,000, range=10,000-16,000,000)
       --depth                     Maximum number of clipping stages. During each split stage, parts with a concavity higher than the user defined threshold are clipped according the "best" clipping plane (default=20, range=1-32)
       --concavity                 Maximum allowed concavity (default=0.0025, range=0.0-1.0)
       --planeDownsampling         Controls the granularity of the search for the "best" clipping plane (default=4, range=1-16)
       --convexhullDownsampling    Controls the precision of the convex-hull generation process during the clipping plane selection stage (default=4, range=1-16)
       --alpha                     Controls the bias toward clipping along symmetry planes (default=0.05, range=0.0-1.0)
       --beta                      Controls the bias toward clipping along revolution axes (default=0.05, range=0.0-1.0)
       --gamma                     Controls the maximum allowed concavity during the merge stage (default=0.00125, range=0.0-1.0)
       --delta                     Controls the bias toward maximaxing local concavity (default=0.05, range=0.0-1.0)
       --pca                       Enable/disable normalizing the mesh before applying the convex decomposition (default=0, range={0,1})
       --mode                      0: voxel-based approximate convex decomposition, 1: tetrahedron-based approximate convex decomposition (default=0, range={0,1})
       --maxNumVerticesPerCH       Controls the maximum number of triangles per convex-hull (default=64, range=4-1024)
       --minVolumePerCH            Controls the adaptive sampling of the generated convex-hulls (default=0.0001, range=0.0-0.01)
       --convexhullApproximation   Enable/disable approximation when computing convex-hulls (default=1, range={0,1})
       --oclAcceleration           Enable/disable OpenCL acceleration (default=0, range={0,1})
       --oclPlatformID             OpenCL platform id (default=0, range=0-# OCL platforms)
       --oclDeviceID               OpenCL device id (default=0, range=0-# OCL devices)
       --help                      Print usage

Examples:
       testVHACD.exe --input bunny.obj --output bunny_acd.obj --log log.txt

+ OpenCL (OFF)
+ Parameters
	 input                                       data/dataset/3763/textured_objs/original-1.obj
	 resolution                                  1000000
	 max. depth                                  20
	 max. concavity                              0.001
	 plane down-sampling                         4
	 convex-hull down-sampling                   4
	 alpha                                       0.05
	 beta                                        0.05
	 gamma                                       0.0005
	 pca                                         0
	 mode                                        0
	 max. vertices per convex-hull               64
	 min. volume to add vertices to convex-hulls 0.0001
	 convex-hull approximation                   1
	 OpenCL acceleration                         1
	 OpenCL platform ID                          0
	 OpenCL device ID                            0
	 output                                      data/dataset/3763/textured_objs/original-1_vhacd.obj
	 log                                         data/dataset/3763/textured_objs/original-1_log.txt
+ Load mesh
+ Voxelization
	 dim = 64	-> 70352 voxels
	 dim = 154	-> 941096 voxels
	 dim = 157	-> 997195 voxels
	 time 0.0922187s
+ Compute primitive set
	 # primitives               997195
	 # inside surface           919513
	 # on surface               77682
	 time 0.00957768s
+ Approximate Convex Decomposition
	 Subdivision level 1
	 -> Part[0] C  = 0.188776, E  = 0.0638267, VS = 77682, VI = 919513
		 [Regular sampling] Number of clipping planes 99

			 Best  0052 T=0.077002 C=0.065533 B=0.002030 S=0.009439 (0.0, 1.0, 0.0, -0.687)

		 [Refining] Number of clipping planes 9

			 Best  0007 T=0.070025 C=0.057964 B=0.002623 S=0.009439 (0.0, 1.0, 0.0, -0.693)

	 Subdivision level 2
	 -> Part[0] C  = 0.00871346, E  = 0.042188, VS = 51346, VI = 618050
	 -> Part[1] C  = 0.0492579, E  = 0.0419514, VS = 51058, VI = 276741
		 [Regular sampling] Number of clipping planes 86

			 Best  0042 T=0.016600 C=0.014071 B=0.000065 S=0.002463 (0.0, 1.0, 0.0, -0.710)

		 [Refining] Number of clipping planes 9

			 Best  0002 T=0.011703 C=0.009071 B=0.000169 S=0.002463 (0.0, 1.0, 0.0, -0.706)

	 Subdivision level 3
	 -> Part[0] C  = 0.00638764, E  = 0.0352205, VS = 42866, VI = 78965
	 -> Part[1] C  = 0.00271272, E  = 0.0270435, VS = 32914, VI = 173054
+ Generate 3 convex-hulls 
	 time 1.80204s
+ Merge Convex Hulls
	 time 0.000675597s
+ Simplify 3 convex-hulls 
		 Simplify CH[00000] 124 V, 244 T
		 Simplify CH[00001] 250 V, 496 T
		 Simplify CH[00002] 116 V, 228 T
	 time 0.00558939s
+ Generate output: 3 convex-hulls 
	 CH[00000] 64 V, 124 T
	 CH[00001] 64 V, 124 T
	 CH[00002] 64 V, 124 T
