<?xml version="1.0" ?>

<robot name="cf2">

  <properties arm="0.0397" kf="3.16e-10" km="7.94e-12" thrust2weight="2.25" max_speed_kmh="30" gnd_eff_coeff="11.36859" prop_radius="2.31348e-2" drag_coeff_xy="9.1785e-7" drag_coeff_z="10.311e-7" dw_coeff_1="2267.18" dw_coeff_2=".16" dw_coeff_3="-.11" />

  <link name="base_link"> 

    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.027"/>
      <inertia ixx="1.4e-5" ixy="0.0" ixz="0.0" iyy="1.4e-5" iyz="0.0" izz="2.17e-5"/>
    </inertial>

    <!-- links>
      <carlos url="https://arxiv.org/pdf/1608.05786.pdf" />
      <julian url="https://www.research-collection.ethz.ch/handle/20.500.11850/214143" />
      <mit url="http://groups.csail.mit.edu/robotics-center/public_papers/Landry15.pdf" />
    </links -->

    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="./body.obj" scale="1 1 1"/>
      </geometry> 
      <material name="grey">
        <color rgba=".5 .5 .5 1"/>
      </material> 
    </visual>

    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder radius=".06" length=".025"/>
      </geometry>
    </collision>  

  </link>

  <link name="prop0_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0.028 -0.028 0"/>
      <mass value="0"/>
      <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0.0323 -0.0323 0.0132"/>
      <geometry>
          <mesh filename="./propeller0.obj"/>
      </geometry> 
    </visual>

  </link>
  <joint name="prop0_joint" type="fixed">
    <parent link="base_link"/>
    <child link="prop0_link"/>
  </joint>

  <link name="prop1_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.028 -0.028 0"/>
      <mass value="0"/>
      <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="-0.0323 -0.0323 0.0132"/>
      <geometry>
          <mesh filename="./propeller1.obj"/>
      </geometry> 
    </visual>
  </link>
  <joint name="prop1_joint" type="fixed">
    <parent link="base_link"/>
    <child link="prop1_link"/>
  </joint>

  <link name="prop2_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.028 0.028 0"/>
      <mass value="0"/>
      <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="-0.0323 0.0323 0.0132"/>
      <geometry>
          <mesh filename="./propeller2.obj"/>
      </geometry> 
    </visual>
  </link>
  <joint name="prop2_joint" type="fixed">
    <parent link="base_link"/>
    <child link="prop2_link"/>
  </joint>

  <link name="prop3_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0.028 0.028 0"/>
      <mass value="0"/>
      <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0.0323 0.0323 0.0132"/>
      <geometry>
          <mesh filename="./propeller3.obj"/>
      </geometry> 
    </visual>
  </link>
  <joint name="prop3_joint" type="fixed">
    <parent link="base_link"/>
    <child link="prop3_link"/>
  </joint>

  <link name="center_of_mass_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0"/>
      <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
    </inertial>
  </link>
  <joint name="center_of_mass_joint" type="fixed">
    <parent link="base_link"/>
    <child link="center_of_mass_link"/>
  </joint>

</robot>

