<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from panda_arm_hand.urdf.xacro      | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="panda" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <link name="hand">
  	<inertial>
      <origin rpy="0 0 0" xyz="0 0 0.04"/>
       <mass value=".81"/>
       <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="package://meshes/visual/hand.obj"/>
      </geometry>
      <material name="panda_white"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://meshes/collision/hand.obj"/>
      </geometry>
      <material name="panda_white"/>
    </collision>
  </link>
  <link name="left_finger">
       <contact>
      <friction_anchor/>
      <stiffness value="30000.0"/>
      <damping value="1000.0"/>
      <spinning_friction value="0.1"/>
      <lateral_friction value="1.0"/>
    </contact>
  	<inertial>
      <origin rpy="0 0 0" xyz="0 0.01 0.02"/>
       <mass value="0.1"/>
       <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="package://meshes/visual/finger.obj"/>
      </geometry>
      <material name="panda_white"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://meshes/collision/finger.obj"/>
      </geometry>
      <material name="panda_white"/>
    </collision>
  </link>
  <link name="right_finger">
        <contact>
      <friction_anchor/>
      <stiffness value="30000.0"/>
      <damping value="1000.0"/>
      <spinning_friction value="0.1"/>
      <lateral_friction value="1.0"/>
    </contact>

  	<inertial>
      <origin rpy="0 0 0" xyz="0 -0.01 0.02"/>
       <mass value="0.1"/>
       <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 3.14159265359" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://meshes/visual/finger.obj"/>
      </geometry>
      <material name="panda_white"/>
    </visual>
    <collision>
      <origin rpy="0 0 3.14159265359" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://meshes/collision/finger.obj"/>
      </geometry>
      <material name="panda_white"/>
    </collision>
  </link>
  <joint name="finger_joint1" type="prismatic">
    <parent link="hand"/>
    <child link="left_finger"/>
    <origin rpy="0 0 0" xyz="0 0 0.0584"/>
    <axis xyz="0 1 0"/>
    <limit effort="20" lower="0.0" upper="0.04" velocity="0.2"/>
  </joint>
  <joint name="finger_joint2" type="prismatic">
    <parent link="hand"/>
    <child link="right_finger"/>
    <origin rpy="0 0 0" xyz="0 0 0.0584"/>
    <axis xyz="0 -1 0"/>
    <limit effort="20" lower="0.0" upper="0.04" velocity="0.2"/>
    <mimic joint="finger_joint1"/>
  </joint>
   <link name="panda_grasptarget">
 <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value="0.0"/>
       <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
   </link>
   <joint name="panda_grasptarget_hand" type="fixed">
    <parent link="hand"/>
    <child link="panda_grasptarget"/>
    <origin rpy="0 0 0" xyz="0 0 0.105"/>
  </joint>
  
</robot>
