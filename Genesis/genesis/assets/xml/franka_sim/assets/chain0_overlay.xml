<mujocoinclude>
<!-- =================================================
    Copyright 2018 Vika<PERSON> 
    Model   :: <PERSON><PERSON> (MuJoCoV2.0)
    Author  :: <PERSON><PERSON><PERSON> (<EMAIL>)
    source  :: https://github.com/vikashplus/franka_sim
    License :: Under Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
====================================================== -->

<!-- Robot limits pulled from https://frankaemika.github.io/docs/control_parameters.html#constants -->

    <body name="_panda0_link0" childclass="panda_overlay" >
        <geom mesh="link0_viz"/>
        <body name="_panda0_link1" pos="0 0 0.333">
            <joint name="_panda0_joint1" range="-2.8973 2.8973"/>
            <geom mesh="link1_viz"/>
            <body name="_panda0_link2" pos="0 0 0" quat="0.707107 -0.707107 0 0">
                <joint name="_panda0_joint2" range="-1.7628 1.7628"/>
                <geom mesh="link2_viz"/>
                <body name="_panda0_link3" pos="0 -0.316 0" quat="0.707107 0.707107 0 0">
                    <joint name="_panda0_joint3" range="-2.8973 2.8973"/>
                    <geom mesh="link3_viz"/>
                    <body name="_panda0_link4" pos="0.0825 0 0" quat="0.707107 0.707107 0 0">
                        <joint name="_panda0_joint4" range="-3.0718 -0.4"/>
                        <geom mesh="link4_viz"/>
                        <body name="_panda0_link5" pos="-0.0825 0.384 0" quat="0.707107 -0.707107 0 0">
                            <joint name="_panda0_joint5" range="-2.8973 2.8973"/>
                            <geom mesh="link5_viz"/>
                            <body name="_panda0_link6" pos="0 0 0" euler="1.57 0 1.57">
                                <joint name="_panda0_joint6" range="-1.6573 2.1127"/>
                                <geom mesh="link6_viz"/>
                                <body name="_panda0_link7" pos="0.088 0 0" euler="1.57 0 0.7854">
                                    <joint name="_panda0_joint7" range="-2.8973 2.8973"/>
                                    <geom mesh="link7_viz"/>
                                    <geom pos="0 0 0.107" quat="0.92388 0 0 -0.382683" mesh="hand_viz"/>
                                    <site name="_end_effector" pos="0 0 .210" size="0.01" euler="0 0 -0.785398"/>
                                    <body name="_panda0_leftfinger" pos="0 0 0.1654" quat="0.92388 0 0 -0.382683">
                                        <joint name="_panda0_finger_joint1"  axis="0 1 0" type="slide" range="0 0.04"/>
                                        <geom mesh="finger_viz"/>
                                    </body>
                                    <body name="_panda0_rightfinger" pos="0 0 0.1654" quat="0.92388 0 0 -0.382683">
                                        <joint name="_panda0_finger_joint2" axis="0 -1 0" type="slide" range="0 0.04"/>
                                        <geom quat="0 0 0 1" mesh="finger_viz"/>
                                    </body>
                                </body>
                            </body>
                        </body>
                    </body>
                </body>
            </body>
        </body>
    </body>
</mujocoinclude>
