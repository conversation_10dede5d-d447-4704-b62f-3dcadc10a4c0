<mujoco model="franka_panda v200">
<!-- =================================================
    Copyright 2018 V<PERSON><PERSON>
    Model   :: <PERSON><PERSON> (MuJoCoV2.0)
    Author  :: <PERSON><PERSON><PERSON> (<EMAIL>)
    source  :: https://github.com/vikashplus/franka_sim
    License :: Under Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
====================================================== -->

    <include file="assets/basic_scene.xml"/>
    <include file="assets/assets.xml"/>
    <include file="assets/gripper_assets.xml"/>
    <compiler meshdir=""/>

    <equality>
      <weld body1="vive_controller" body2="panda0_link7" solref="0.01 1" solimp=".25 .25 0.001"/>
    </equality>


    <worldbody>
        <!-- Mocap -->
        <body name="vive_controller" mocap="true" pos="0 0 1.895" euler="-1.57 0 -.785">
            <geom type="box" group="2" pos='0 0 .142' size="0.02 0.10 0.03" contype="0" conaffinity="0" rgba=".9 .7 .95 .2" euler="0 0 -.785"/>
        </body>

        <!-- Robot -->
        <body pos='0 0 .775' euler='0 0 1.57'>
            <geom type='cylinder' size='.120 .4' pos='-.04 0 -.4'/>
            <include file="assets/chain0.xml"/>
            <include file="assets/chain0_overlay.xml"/>
        </body>

    </worldbody>


    <include file='assets/teleop_actuator.xml'/>
</mujoco>
