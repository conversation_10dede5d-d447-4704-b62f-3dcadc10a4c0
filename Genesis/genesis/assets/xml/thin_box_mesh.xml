<mujoco model="ant">
  <compiler angle="degree" coordinate="local" inertiafromgeom="true"/>
  <option timestep="0.01" iterations="4"/>

  <default>
    <joint armature="1" damping="1" limited="true"/>
    <geom contype="0" conaffinity="0" condim="3" density="5.0" friction="1 0.5 0.5" rgba="0.8 0.6 0.4 1"/>
  </default>
  <asset>
    <texture builtin="gradient" height="100" rgb1="1 1 1" rgb2="0 0 0" type="skybox" width="100"/>
    <texture builtin="flat" height="1278" mark="cross" markrgb="1 1 1" name="texgeom" random="0.01" rgb1="0.8 0.6 0.4" rgb2="0.8 0.6 0.4" type="cube" width="127"/>
    <texture builtin="checker" height="100" name="texplane" rgb1="0 0 0" rgb2="0.8 0.8 0.8" type="2d" width="100"/>
    <material name="MatPlane" reflectance="0.5" shininess="1" specular="1" texrepeat="60 60" texture="texplane"/>
    <material name="geom" texture="texgeom" texuniform="true"/>
    <mesh name="box" file="thin_cube.obj"/>
  </asset>


  <worldbody>
    <light cutoff="100" diffuse="1 1 1" dir="-0 0 -1.3" directional="true" exponent="1" pos="0 0 1.3" specular=".1 .1 .1"/>
    <geom name="floor" contype="1" conaffinity="1" pos="0 0 -0.1" type="box" size="10. 10. 0.2"/>
    <!-- <geom conaffinity="1" condim="3" material="MatPlane" name="floor" pos="0 0 0" rgba="0.8 0.9 0.8 1" size="40 40 40" type="plane"/> -->


    <body name="tet1" pos="0.1 0.1 0.1">
      <geom name="tet1" contype="1" conaffinity="1" pos="0 0 0" type="mesh" mesh="box"/>
      <joint armature="0" damping="0" limited="false" margin="0.01" name="root1" pos="0 0 0" type="free"/>
    </body>

    <!-- <body name="box2" pos="-0.15 -0.05 0.2">
      <geom name="box2" contype="1" conaffinity="1" pos="0 0 0" type="box" size="0.2 0.2 0.1"/>
      <joint armature="0" damping="0" limited="false" margin="0.01" name="root2" pos="0 0 0" type="free"/>
    </body> -->

  </worldbody>
</mujoco>
