<mujoco model="ur5e scene">
  <include file="ur5e.xml"/>

  <statistic center="0.3 0 0.3" extent="0.8" meansize="0.08"/>

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.1 0.1 0.1" specular="0 0 0"/>
    <global azimuth="120" elevation="-20"/>
  </visual>

  <asset>
    <texture name="grid" type="2d" builtin="checker" rgb1=".2 .3 .4" rgb2=".1 0.15 0.2" width="512" height="512"
      mark="cross" markrgb=".8 .8 .8"/>
    <material name="grid" texture="grid" texrepeat="1 1" texuniform="true"/>
  </asset>

  <worldbody>
    <light pos="0 0 1.5" directional="true"/>
    <geom name="floor" size="1 1 0.01" type="plane" material="grid"/>
    <body name="target" pos="0.5 0 .5" quat="0 1 0 0" mocap="true">
      <geom type="box" size=".05 .05 .05" contype="0" conaffinity="0" rgba=".6 .3 .3 .5"/>
      <site type="sphere" size="0.01" rgba="0 0 1 1" group="1"/>
    </body>
  </worldbody>
</mujoco>
