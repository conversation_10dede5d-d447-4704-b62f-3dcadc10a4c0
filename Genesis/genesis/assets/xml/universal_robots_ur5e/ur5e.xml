<mujoco model="ur5e">
  <compiler angle="radian" meshdir="assets" autolimits="true"/>

  <option integrator="implicitfast"/>

  <default>
    <default class="ur5e">
      <default class="visual">
        <geom type="mesh" contype="0" conaffinity="0" group="2"/>
      </default>
      <default class="collision">
        <geom type="capsule" group="3"/>
        <default class="eef_collision">
          <geom type="cylinder"/>
        </default>
      </default>
      <joint axis="0 1 0" range="-6.28319 6.28319"/>
      <default class="joint_limited">
        <joint range="-3.1415 3.1415"/>
      </default>
      <default class="position">
        <position ctrlrange="-6.2831 6.2831" kp="2000" kv="100" forcerange="-150 150"/>
        <default class="position_limited">
          <position ctrlrange="-3.1415 3.1415"/>
        </default>
        <default class="position_small">
          <position kp="500" kv="25" forcerange="-28 28"/>
        </default>
      </default>
    </default>
  </default>

  <asset>
    <material name="black" rgba="0.033 0.033 0.033 1"/>
    <material name="jointgray" rgba="0.278 0.278 0.278 1"/>
    <material name="linkgray" rgba="0.82 0.82 0.82 1"/>
    <material name="urblue" rgba="0.49 0.678 0.8 1"/>

    <mesh file="base_0.obj"/>
    <mesh file="base_1.obj"/>
    <mesh file="shoulder_0.obj"/>
    <mesh file="shoulder_1.obj"/>
    <mesh file="shoulder_2.obj"/>
    <mesh file="upperarm_0.obj"/>
    <mesh file="upperarm_1.obj"/>
    <mesh file="upperarm_2.obj"/>
    <mesh file="upperarm_3.obj"/>
    <mesh file="forearm_0.obj"/>
    <mesh file="forearm_1.obj"/>
    <mesh file="forearm_2.obj"/>
    <mesh file="forearm_3.obj"/>
    <mesh file="wrist1_0.obj"/>
    <mesh file="wrist1_1.obj"/>
    <mesh file="wrist1_2.obj"/>
    <mesh file="wrist2_0.obj"/>
    <mesh file="wrist2_1.obj"/>
    <mesh file="wrist2_2.obj"/>
    <mesh file="wrist3.obj"/>
  </asset>

  <worldbody>
    <body name="base" quat="1 0 0 1" childclass="ur5e">
      <inertial mass="4" pos="0 0 0" diaginertia="0.00443333156 0.00443333156 0.0072"/>
      <geom mesh="base_0" material="black" class="visual"/>
      <geom mesh="base_1" material="jointgray" class="visual"/>
      <body name="shoulder_link" pos="0 0 0.163">
        <inertial mass="3.7" pos="0 0 0" diaginertia="0.0102675 0.0102675 0.00666"/>
        <joint name="shoulder_pan" axis="0 0 1"/>
        <geom mesh="shoulder_0" material="urblue" class="visual"/>
        <geom mesh="shoulder_1" material="black" class="visual"/>
        <geom mesh="shoulder_2" material="jointgray" class="visual"/>
        <geom class="collision" size="0.06 0.06" pos="0 0 -0.04"/>
        <body name="upper_arm_link" pos="0 0.138 0" quat="1 0 1 0">
          <inertial mass="8.393" pos="0 0 0.2125" diaginertia="0.133886 0.133886 0.0151074"/>
          <joint name="shoulder_lift"/>
          <geom mesh="upperarm_0" material="linkgray" class="visual"/>
          <geom mesh="upperarm_1" material="black" class="visual"/>
          <geom mesh="upperarm_2" material="jointgray" class="visual"/>
          <geom mesh="upperarm_3" material="urblue" class="visual"/>
          <geom class="collision" pos="0 -0.04 0" quat="1 1 0 0" size="0.06 0.06"/>
          <geom class="collision" size="0.05 0.2" pos="0 0 0.2"/>
          <body name="forearm_link" pos="0 -0.131 0.425">
            <inertial mass="2.275" pos="0 0 0.196" diaginertia="0.0311796 0.0311796 0.004095"/>
            <joint name="elbow" class="joint_limited"/>
            <geom mesh="forearm_0" material="urblue" class="visual"/>
            <geom mesh="forearm_1" material="linkgray" class="visual"/>
            <geom mesh="forearm_2" material="black" class="visual"/>
            <geom mesh="forearm_3" material="jointgray" class="visual"/>
            <geom class="collision" pos="0 0.08 0" quat="1 1 0 0" size="0.055 0.06"/>
            <geom class="collision" size="0.038 0.19" pos="0 0 0.2"/>
            <body name="wrist_1_link" pos="0 0 0.392" quat="1 0 1 0">
              <inertial mass="1.219" pos="0 0.127 0" diaginertia="0.0025599 0.0025599 0.0021942"/>
              <joint name="wrist_1"/>
              <geom mesh="wrist1_0" material="black" class="visual"/>
              <geom mesh="wrist1_1" material="urblue" class="visual"/>
              <geom mesh="wrist1_2" material="jointgray" class="visual"/>
              <geom class="collision" pos="0 0.05 0" quat="1 1 0 0" size="0.04 0.07"/>
              <body name="wrist_2_link" pos="0 0.127 0">
                <inertial mass="1.219" pos="0 0 0.1" diaginertia="0.0025599 0.0025599 0.0021942"/>
                <joint name="wrist_2" axis="0 0 1"/>
                <geom mesh="wrist2_0" material="black" class="visual"/>
                <geom mesh="wrist2_1" material="urblue" class="visual"/>
                <geom mesh="wrist2_2" material="jointgray" class="visual"/>
                <geom class="collision" size="0.04 0.06" pos="0 0 0.04"/>
                <geom class="collision" pos="0 0.02 0.1" quat="1 1 0 0" size="0.04 0.04"/>
                <body name="wrist_3_link" pos="0 0 0.1">
                  <inertial mass="0.1879" pos="0 0.0771683 0" quat="1 0 0 1"
                    diaginertia="0.000132134 9.90863e-05 9.90863e-05"/>
                  <joint name="wrist_3"/>
                  <geom material="linkgray" mesh="wrist3" class="visual"/>
                  <geom class="eef_collision" pos="0 0.08 0" quat="1 1 0 0" size="0.04 0.02"/>
                  <site name="attachment_site" size="0.01" pos="0 0.1 0" quat="-1 1 1 1" rgba="1 0 0 1" group="1"/>
                  <body name="ee_virtual_link" pos="0 0.1 0" quat="-1 1 1 1"/>
                </body>
              </body>
            </body>
          </body>
        </body>
      </body>
    </body>
  </worldbody>

  <actuator>
    <position class="position" name="shoulder_pan" joint="shoulder_pan"/>
    <position class="position" name="shoulder_lift" joint="shoulder_lift"/>
    <position class="position_limited" name="elbow" joint="elbow"/>
    <position class="position_small" name="wrist_1" joint="wrist_1"/>
    <position class="position_small" name="wrist_2" joint="wrist_2"/>
    <position class="position_small" name="wrist_3" joint="wrist_3"/>
  </actuator>

  <keyframe>
    <key name="home" qpos="-1.5708 -1.5708 1.5708 -1.5708 -1.5708 0" ctrl="-1.5708 -1.5708 1.5708 -1.5708 -1.5708 0"/>
  </keyframe>
</mujoco>
