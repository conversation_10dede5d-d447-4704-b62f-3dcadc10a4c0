# 🎮 PS5 Controller Setup for Robot Control

## Overview
This guide will help you set up PS5 DualSense controller support for controlling the humanoid robots in the sim2sim demos.

## Current Status
✅ **Keyboard Control**: Working perfectly with `play_g1_keyboard.py`
⚠️ **PS5 Controller**: In development (pygame compatibility issues on macOS)

## Working Controls

### Keyboard Control (Available Now)
Run the G1 robot with keyboard control:
```bash
python3 play_g1_keyboard.py
```

**Controls:**
- `W/S`: Forward/Backward
- `A/D`: Strafe Left/Right  
- `Q/E`: Turn Left/Right
- `Space`: Stop
- `ESC`: Exit

## PS5 Controller Setup Instructions

### Step 1: Connect Your PS5 Controller

#### Option A: USB Connection (Recommended)
1. Connect your PS5 DualSense controller to your Mac via USB-C cable
2. The controller should be automatically detected

#### Option B: Bluetooth Connection
1. Put your PS5 controller in pairing mode:
   - Hold the **PS button** + **Share button** for 3 seconds
   - The light bar will start flashing blue
2. On macOS:
   - Go to **System Preferences** > **Bluetooth**
   - Look for "DualSense Wireless Controller"
   - Click **Connect**

### Step 2: Verify Controller Detection
Test if your controller is detected:
```bash
python3 ps5_controller.py
```

This will show if your controller is found and display real-time input values.

### Step 3: Run Robot with PS5 Control
Once your controller is working:
```bash
python3 play_g1_ps5.py        # G1 robot
python3 play_apollo_ps5.py    # Apollo robot (when available)
```

## PS5 Controller Mapping

### Analog Sticks
- **Left Stick**: 
  - Up/Down: Forward/Backward movement
  - Left/Right: Strafe left/right
- **Right Stick**:
  - Left/Right: Turn left/right

### Buttons (Future Features)
- **Triangle**: Speed boost
- **Square**: Slow mode
- **Circle**: Stop
- **X**: Reset position

## Troubleshooting

### Controller Not Detected
1. **Check USB Connection**: Try a different USB-C cable
2. **Bluetooth Issues**: 
   - Unpair and re-pair the controller
   - Restart Bluetooth service: `sudo pkill bluetoothd`
3. **Driver Issues**: Update macOS to latest version

### pygame Issues on macOS
If you encounter pygame crashes:
1. Try the keyboard control version instead
2. Install pygame via conda: `conda install pygame`
3. Use virtual environment with specific pygame version

### Permission Issues
If you get permission errors:
```bash
sudo chmod +x ps5_controller.py
```

## Alternative Control Methods

### 1. Keyboard Control ✅
- **File**: `play_g1_keyboard.py`
- **Status**: Working perfectly
- **Best for**: Testing and development

### 2. Xbox Controller
- Similar setup to PS5 controller
- May have better macOS compatibility
- Use same pygame-based approach

### 3. Custom Joystick
- Any USB gamepad should work
- Modify controller mapping in code
- Test with `pygame.joystick.get_count()`

## Development Notes

### Current Implementation
- Uses `pygame` for controller input
- Real-time command mapping to robot velocities
- Deadzone handling for precise control
- Fallback to autonomous mode if controller disconnected

### Known Issues
- pygame initialization crashes on some macOS systems
- Bluetooth latency can affect responsiveness
- Controller mapping may vary between different PS5 controller revisions

### Future Improvements
- [ ] Native macOS controller support (without pygame)
- [ ] Controller calibration interface
- [ ] Multiple controller support
- [ ] Custom button mapping
- [ ] Haptic feedback integration

## Files Overview

| File | Purpose | Status |
|------|---------|--------|
| `ps5_controller.py` | PS5 controller test/debug | ⚠️ Development |
| `play_g1_keyboard.py` | G1 with keyboard control | ✅ Working |
| `play_g1_ps5.py` | G1 with PS5 controller | ⚠️ Development |
| `play_apollo_ps5.py` | Apollo with PS5 controller | ⚠️ Development |

## Getting Help

If you encounter issues:
1. Try the keyboard control version first
2. Check controller detection with the test script
3. Verify pygame installation: `python3 -c "import pygame; print('OK')"`
4. Check for macOS permission prompts for input devices

## Success! 🎉

Once working, you'll have:
- Real-time robot control with PS5 controller
- Intuitive movement mapping
- Smooth analog control
- Multiple robot support (G1, Apollo, etc.)

The robots will respond to your controller input in real-time, allowing you to walk them around, change direction, and control their speed naturally!
