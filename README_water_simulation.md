# Optimized Genesis Water Simulation

This is an optimized water simulation using the Genesis physics engine with multiple simulation types and performance presets.

## Features

- **Multiple Simulation Types**: SPH, PBD, Emitter, and optimized Fast SPH
- **Performance Presets**: Choose between performance, balanced, and quality modes
- **Particle Count Options**: Low, medium, and high particle counts
- **GPU Acceleration**: Optimized for GPU backends
- **Real-time FPS Monitoring**: Track simulation performance

## Usage

### Quick Start (Recommended)
```bash
# Run optimized fast SPH simulation with balanced settings
python3 water_simulation.py

# Run with performance mode for maximum speed
python3 water_simulation.py --quality performance --particle_count low

# Run demo of all simulation types
python3 water_simulation.py --sim_type demo
```

### Command Line Options

```bash
python3 water_simulation.py [OPTIONS]

Options:
  -v, --vis              Show visualization (default: True)
  --sim_type {sph,pbd,emitter,fast_sph,demo}
                        Type of water simulation (default: fast_sph)
  --steps INT           Number of simulation steps (default: 500)
  --particle_count {low,medium,high}
                        Particle count preset (default: medium)
  --quality {performance,balanced,quality}
                        Quality vs performance preset (default: balanced)
```

### Simulation Types

1. **fast_sph** (Recommended): Optimized SPH with performance enhancements
2. **sph**: Standard SPH simulation with full features
3. **pbd**: Position-Based Dynamics simulation
4. **emitter**: Water droplet emission simulation
5. **demo**: Quick demo of all simulation types

### Performance Presets

- **performance**: Maximum speed, lower quality (8ms timestep, 5 substeps)
- **balanced**: Good balance of speed and quality (4ms timestep, 8 substeps)
- **quality**: Best quality, slower speed (2ms timestep, 15 substeps)

### Particle Count Presets

- **low**: ~27K particles (0.025m particle size)
- **medium**: ~177K particles (0.015m particle size)  
- **high**: ~512K particles (0.01m particle size)

## Optimizations Applied

1. **GPU Backend**: Uses Metal/CUDA for acceleration
2. **Reduced Solver Iterations**: Balanced accuracy vs performance
3. **Optimized Visual Settings**: Disabled expensive visual effects
4. **Memory Management**: Efficient GPU memory usage
5. **Adaptive Quality**: Quality presets for different use cases
6. **WCSPH Solver**: Faster pressure solver for real-time performance

## Performance Tips

- Use `--quality performance` for maximum FPS
- Use `--particle_count low` for faster simulation
- Disable visualization with `--vis` flag for headless mode
- Monitor FPS output to tune settings for your hardware

## Requirements

- Genesis physics engine
- GPU with Metal (macOS) or CUDA (Linux/Windows) support
- Python 3.8+
- NumPy

## Example Commands

```bash
# Maximum performance
python3 water_simulation.py --quality performance --particle_count low --steps 1000

# High quality simulation
python3 water_simulation.py --quality quality --particle_count high --steps 200

# Water fountain demo
python3 water_simulation.py --sim_type emitter --quality balanced

# Quick performance test
python3 water_simulation.py --sim_type demo
```
