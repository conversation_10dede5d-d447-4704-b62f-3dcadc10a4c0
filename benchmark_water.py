#!/usr/bin/env python3
"""
Benchmark script for Genesis water simulations
Tests different configurations and reports performance metrics
"""

import argparse
import time
import genesis as gs
import numpy as np


def benchmark_configuration(sim_type, particle_count, quality, steps=200):
    """Benchmark a specific configuration"""
    print(f"\n--- Benchmarking {sim_type.upper()} | {particle_count} particles | {quality} quality ---")
    
    # Import the simulation functions
    from water_simulation import (
        get_particle_settings, 
        run_fast_sph_simulation, 
        run_pbd_simulation,
        run_emitter_simulation
    )
    
    # Create args object
    args = type('Args', (), {
        'vis': False,  # Disable visualization for benchmarking
        'steps': steps,
        'particle_count': particle_count,
        'quality': quality,
        'sim_type': sim_type
    })()
    
    start_time = time.time()
    
    try:
        if sim_type == "fast_sph":
            run_fast_sph_simulation(args)
        elif sim_type == "pbd":
            run_pbd_simulation(args)
        elif sim_type == "emitter":
            run_emitter_simulation(args)
        
        total_time = time.time() - start_time
        avg_fps = steps / total_time
        
        particle_settings, _ = get_particle_settings(particle_count, quality)
        
        return {
            'sim_type': sim_type,
            'particle_count': particle_count,
            'quality': quality,
            'total_time': total_time,
            'avg_fps': avg_fps,
            'particle_size': particle_settings['particle_size'],
            'steps': steps
        }
        
    except Exception as e:
        print(f"Error in {sim_type}: {e}")
        return None


def run_benchmark_suite():
    """Run comprehensive benchmark suite"""
    print("Genesis Water Simulation Benchmark Suite")
    print("=" * 50)
    
    # Initialize Genesis once
    gs.init(backend=gs.gpu, seed=0, precision="32", logging_level="error")
    
    configurations = [
        # Fast SPH tests
        ("fast_sph", "low", "performance"),
        ("fast_sph", "low", "balanced"),
        ("fast_sph", "medium", "performance"),
        ("fast_sph", "medium", "balanced"),
        ("fast_sph", "high", "performance"),
        
        # PBD tests
        ("pbd", "low", "performance"),
        ("pbd", "medium", "performance"),
        ("pbd", "high", "performance"),
        
        # Emitter tests
        ("emitter", "low", "performance"),
        ("emitter", "medium", "performance"),
    ]
    
    results = []
    
    for sim_type, particle_count, quality in configurations:
        result = benchmark_configuration(sim_type, particle_count, quality, steps=150)
        if result:
            results.append(result)
    
    # Print summary
    print("\n" + "=" * 80)
    print("BENCHMARK RESULTS SUMMARY")
    print("=" * 80)
    print(f"{'Simulation':<12} {'Particles':<10} {'Quality':<12} {'FPS':<8} {'Time(s)':<8} {'P.Size':<8}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['sim_type']:<12} {result['particle_count']:<10} {result['quality']:<12} "
              f"{result['avg_fps']:<8.1f} {result['total_time']:<8.1f} {result['particle_size']:<8.3f}")
    
    # Find best performers
    print("\n" + "=" * 50)
    print("PERFORMANCE HIGHLIGHTS")
    print("=" * 50)
    
    fastest = max(results, key=lambda x: x['avg_fps'])
    print(f"Fastest: {fastest['sim_type']} ({fastest['particle_count']}, {fastest['quality']}) - {fastest['avg_fps']:.1f} FPS")
    
    sph_results = [r for r in results if r['sim_type'] == 'fast_sph']
    if sph_results:
        best_sph = max(sph_results, key=lambda x: x['avg_fps'])
        print(f"Best SPH: {best_sph['particle_count']}, {best_sph['quality']} - {best_sph['avg_fps']:.1f} FPS")
    
    pbd_results = [r for r in results if r['sim_type'] == 'pbd']
    if pbd_results:
        best_pbd = max(pbd_results, key=lambda x: x['avg_fps'])
        print(f"Best PBD: {best_pbd['particle_count']}, {best_pbd['quality']} - {best_pbd['avg_fps']:.1f} FPS")


def quick_performance_test():
    """Quick performance test with optimal settings"""
    print("Quick Performance Test - Optimal Settings")
    print("=" * 45)
    
    gs.init(backend=gs.gpu, seed=0, precision="32", logging_level="error")
    
    # Test the fastest configuration
    result = benchmark_configuration("fast_sph", "low", "performance", steps=300)
    if result:
        print(f"\nOptimal Performance: {result['avg_fps']:.1f} FPS")
        print(f"Particle count: ~{int(27000 * (0.025/result['particle_size'])**3):,}")
        print(f"Recommended for real-time applications: {'Yes' if result['avg_fps'] > 20 else 'No'}")


def main():
    parser = argparse.ArgumentParser(description="Benchmark Genesis Water Simulations")
    parser.add_argument("--mode", choices=["quick", "full"], default="quick",
                       help="Benchmark mode (default: quick)")
    args = parser.parse_args()
    
    if args.mode == "quick":
        quick_performance_test()
    else:
        run_benchmark_suite()


if __name__ == "__main__":
    main()
