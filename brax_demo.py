#!/usr/bin/env python3
"""
Simple Brax demo showing environment loading and basic functionality.
This demonstrates the MuJoCo Playground environments without the training complexity.
"""

import os
import sys
import jax
import jax.numpy as jp
import numpy as np

# Add the parent directory to path to import mujoco_playground
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import mujoco_playground
    from mujoco_playground import registry
    from mujoco_playground import wrapper
except ImportError as e:
    print(f"Error importing mujoco_playground: {e}")
    print("Make sure you're running from the correct directory")
    sys.exit(1)


def demo_environment(env_name, num_steps=100):
    """Demo a single environment."""
    print(f"\n{'='*60}")
    print(f"🚀 BRAX DEMO: {env_name}")
    print(f"{'='*60}")
    
    try:
        # Load environment
        print(f"Loading environment: {env_name}")
        env_cfg = registry.get_default_config(env_name)
        env = registry.load(env_name, config=env_cfg)
        
        print(f"Environment loaded successfully!")
        print(f"Observation space: {env.observation_size}")
        print(f"Action space: {env.action_size}")
        print(f"Episode length: {env_cfg.episode_length}")
        
        # Reset environment
        rng = jax.random.PRNGKey(42)
        state = env.reset(rng)
        
        print(f"Environment reset. Initial state shape: {state.obs.shape}")
        
        # Run some steps
        total_reward = 0
        for step in range(num_steps):
            # Random action
            action_rng, rng = jax.random.split(rng)
            action = jax.random.uniform(
                action_rng, 
                (env.action_size,), 
                minval=-1.0, 
                maxval=1.0
            )
            
            # Step environment
            state = env.step(state, action)
            total_reward += state.reward
            
            if step % 20 == 0:
                print(f"Step {step}: reward={state.reward:.3f}, done={state.done}")
            
            if state.done:
                print(f"Episode finished at step {step}")
                break
        
        print(f"Demo completed!")
        print(f"Total reward: {total_reward:.3f}")
        print(f"Final step: {step}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with {env_name}: {e}")
        return False


def demo_multiple_environments():
    """Demo multiple environments in parallel."""
    print(f"\n{'='*60}")
    print(f"🚀 BRAX DEMO: Multiple Environments")
    print(f"{'='*60}")
    
    env_name = "CartpoleBalance"
    num_envs = 4
    
    try:
        # Load environment
        env_cfg = registry.get_default_config(env_name)
        env = registry.load(env_name, config=env_cfg)
        
        print(f"Running {num_envs} parallel environments of {env_name}")
        
        # Reset multiple environments
        rng = jax.random.PRNGKey(42)
        reset_rng = jax.random.split(rng, num_envs)
        state = jax.vmap(env.reset)(reset_rng)
        
        print(f"Parallel environments reset. State shape: {state.obs.shape}")
        
        # Run some steps
        total_rewards = jp.zeros(num_envs)
        for step in range(50):
            # Random actions for all environments
            action_rng, rng = jax.random.split(rng)
            actions = jax.random.uniform(
                action_rng, 
                (num_envs, env.action_size), 
                minval=-1.0, 
                maxval=1.0
            )
            
            # Step all environments
            state = jax.vmap(env.step)(state, actions)
            total_rewards += state.reward
            
            if step % 10 == 0:
                avg_reward = jp.mean(state.reward)
                print(f"Step {step}: avg_reward={avg_reward:.3f}")
        
        print(f"Parallel demo completed!")
        print(f"Average total reward: {jp.mean(total_rewards):.3f}")
        print(f"Reward range: [{jp.min(total_rewards):.3f}, {jp.max(total_rewards):.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with parallel demo: {e}")
        return False


def list_available_environments():
    """List all available environments."""
    print(f"\n{'='*60}")
    print(f"📋 AVAILABLE ENVIRONMENTS")
    print(f"{'='*60}")
    
    try:
        all_envs = registry.ALL_ENVS
        print(f"Total environments available: {len(all_envs)}")
        
        # Group by category
        dm_control = [env for env in all_envs if env in mujoco_playground.dm_control_suite.ALL_ENVS]
        locomotion = [env for env in all_envs if env in mujoco_playground.locomotion.ALL_ENVS]
        manipulation = [env for env in all_envs if env in mujoco_playground.manipulation.ALL_ENVS]
        
        print(f"\n🎮 DM Control Suite ({len(dm_control)} envs):")
        for env in dm_control[:5]:  # Show first 5
            print(f"  - {env}")
        if len(dm_control) > 5:
            print(f"  ... and {len(dm_control) - 5} more")
        
        print(f"\n🦿 Locomotion ({len(locomotion)} envs):")
        for env in locomotion[:5]:  # Show first 5
            print(f"  - {env}")
        if len(locomotion) > 5:
            print(f"  ... and {len(locomotion) - 5} more")
        
        print(f"\n🤖 Manipulation ({len(manipulation)} envs):")
        for env in manipulation[:5]:  # Show first 5
            print(f"  - {env}")
        if len(manipulation) > 5:
            print(f"  ... and {len(manipulation) - 5} more")
            
    except Exception as e:
        print(f"❌ Error listing environments: {e}")


def main():
    """Run the Brax demo."""
    print("🤖 MuJoCo Playground + Brax Demo")
    print("This demo shows the Brax-based environments in action.")
    
    # Check JAX setup
    print(f"\n🔧 System Info:")
    print(f"JAX version: {jax.__version__}")
    print(f"JAX devices: {jax.devices()}")
    print(f"JAX default backend: {jax.default_backend()}")
    
    # List available environments
    list_available_environments()
    
    # Demo simple environments
    simple_envs = ["CartpoleBalance", "PendulumSwingup", "ReacherEasy"]
    
    success_count = 0
    for env_name in simple_envs:
        if demo_environment(env_name, num_steps=50):
            success_count += 1
    
    # Demo parallel environments
    if demo_multiple_environments():
        success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🎉 DEMO SUMMARY")
    print(f"{'='*60}")
    print(f"Successful demos: {success_count}/{len(simple_envs) + 1}")
    
    if success_count > 0:
        print("✅ Brax environments are working!")
        print("\nKey features demonstrated:")
        print("  🔄 Environment loading and configuration")
        print("  🎯 Single environment simulation")
        print("  🚀 Parallel environment simulation")
        print("  📊 State and action space handling")
        print("  🎮 Random policy rollouts")
        
        print(f"\nTo run full PPO training (when working):")
        print(f"  python3 train_jax_ppo.py --env_name=CartpoleBalance --num_timesteps=50000")
    else:
        print("❌ Some issues encountered. Check the error messages above.")


if __name__ == '__main__':
    main()
