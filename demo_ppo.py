#!/usr/bin/env python3
"""
Demo script showing the PPO agent capabilities.
This script demonstrates training and evaluation on different environments.
"""

import subprocess
import sys
import time


def run_command(cmd, description):
    """Run a command and print the description."""
    print(f"\n{'='*60}")
    print(f"DEMO: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ SUCCESS")
            # Print last few lines of output
            lines = result.stdout.strip().split('\n')
            if len(lines) > 10:
                print("... (output truncated)")
                for line in lines[-10:]:
                    print(line)
            else:
                print(result.stdout)
        else:
            print("❌ FAILED")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ TIMEOUT (5 minutes)")
    except Exception as e:
        print(f"💥 ERROR: {e}")


def main():
    """Run the PPO demo."""
    print("🤖 PPO Agent Demo")
    print("This demo shows the PPO agent working with different environments.")
    
    # Test 1: Basic functionality test
    run_command(
        "python3 test_ppo.py",
        "Running basic functionality tests"
    )
    
    # Test 2: Train on dummy environment (quick)
    run_command(
        "python3 run_ppo.py --env dummy --train --episodes 20",
        "Training PPO on dummy environment (20 episodes)"
    )
    
    # Test 3: Evaluate dummy environment
    run_command(
        "python3 run_ppo.py --env dummy --eval --episodes 5 --model-path ppo_model.pth",
        "Evaluating trained PPO agent on dummy environment"
    )
    
    # Test 4: Train on CartPole (if available)
    run_command(
        "python3 run_ppo.py --env cartpole --train --episodes 50",
        "Training PPO on CartPole environment (50 episodes)"
    )
    
    # Test 5: Evaluate CartPole
    run_command(
        "python3 run_ppo.py --env cartpole --eval --episodes 5 --model-path ppo_model.pth",
        "Evaluating trained PPO agent on CartPole"
    )
    
    # Test 6: Try Genesis Go2 environment (if available)
    run_command(
        "python3 run_ppo.py --env go2 --train --episodes 5",
        "Testing Genesis Go2 environment (5 episodes)"
    )
    
    print(f"\n{'='*60}")
    print("🎉 DEMO COMPLETED!")
    print("The PPO agent has been successfully demonstrated.")
    print("\nKey features shown:")
    print("✅ Actor-Critic neural network")
    print("✅ PPO algorithm implementation")
    print("✅ Multi-environment support (Gym, Genesis, Dummy)")
    print("✅ Continuous and discrete action spaces")
    print("✅ Model saving and loading")
    print("✅ Training and evaluation modes")
    print("✅ Automatic device detection (CPU/GPU)")
    print("\nYou can now use the PPO agent with:")
    print("  python3 run_ppo.py --env <env_name> --train --episodes <num>")
    print("  python3 run_ppo.py --env <env_name> --eval --model-path <path>")
    print(f"{'='*60}")


if __name__ == '__main__':
    main()
