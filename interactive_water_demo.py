#!/usr/bin/env python3
"""
Interactive Genesis Water Simulation Demo
Real-time water simulation with interactive controls
"""

import numpy as np
import genesis as gs
import time


def create_interactive_water_scene():
    """Create an interactive water scene with optimal performance settings"""
    print("Creating Interactive Water Scene...")
    
    # Initialize Genesis with GPU backend
    gs.init(backend=gs.gpu, seed=0, precision="32", logging_level="warning")
    
    # Create optimized scene
    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=6e-3,  # Balanced timestep
            substeps=6,
            substeps_local=1,
        ),
        sph_options=gs.options.SPHOptions(
            lower_bound=(-1.5, -1.5, 0.0),
            upper_bound=(1.5, 1.5, 2.5),
            particle_size=0.02,  # Good balance of performance and visual quality
            pressure_solver="WCSPH",
            max_density_solver_iterations=40,
        ),
        vis_options=gs.options.VisOptions(
            visualize_sph_boundary=False,
            show_world_frame=False,
            shadow=True,  # Keep shadows for better visuals
            plane_reflection=True,  # Nice visual effect
            particle_size_scale=0.9,
            background_color=(0.1, 0.15, 0.2),
            ambient_light=(0.3, 0.3, 0.3),
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(3.0, -2.5, 2.0),
            camera_lookat=(0.0, 0.0, 0.5),
            camera_fov=50,
            max_FPS=60,
            res=(1280, 720),
        ),
        show_viewer=True,
    )
    
    # Ground plane
    scene.add_entity(morph=gs.morphs.Plane())
    
    # Main water pool
    water_pool = scene.add_entity(
        material=gs.materials.SPH.Liquid(
            rho=1000.0,
            stiffness=40000.0,
            mu=0.008,  # Slight viscosity for realism
            gamma=0.015,  # Surface tension
            sampler="regular"
        ),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 0.4),
            size=(1.0, 1.0, 0.8),
        ),
        surface=gs.surfaces.Default(
            color=(0.2, 0.6, 1.0, 0.8),
            vis_mode="particle",
        ),
    )
    
    # Interactive objects - floating cubes
    cube1 = scene.add_entity(
        material=gs.materials.Rigid(needs_coup=True, coup_friction=0.2),
        morph=gs.morphs.Box(
            pos=(0.3, 0.3, 1.2),
            size=(0.12, 0.12, 0.12),
            euler=(30, 45, 0),
            fixed=False,
        ),
        surface=gs.surfaces.Default(
            color=(0.9, 0.3, 0.3, 1.0),
        ),
    )
    
    cube2 = scene.add_entity(
        material=gs.materials.Rigid(needs_coup=True, coup_friction=0.2),
        morph=gs.morphs.Box(
            pos=(-0.3, -0.3, 1.5),
            size=(0.1, 0.1, 0.1),
            euler=(60, 30, 45),
            fixed=False,
        ),
        surface=gs.surfaces.Default(
            color=(0.3, 0.9, 0.3, 1.0),
        ),
    )
    
    # Water emitter for interactive drops
    emitter = scene.add_emitter(
        material=gs.materials.SPH.Liquid(
            sampler="regular",
            mu=0.008,
            gamma=0.015
        ),
        max_particles=20000,
        surface=gs.surfaces.Default(
            color=(0.5, 0.8, 1.0, 0.9),
            vis_mode="particle",
        ),
    )
    
    scene.build()
    
    return scene, emitter


def run_interactive_simulation():
    """Run the interactive water simulation"""
    scene, emitter = create_interactive_water_scene()
    
    print("\n" + "="*60)
    print("INTERACTIVE WATER SIMULATION")
    print("="*60)
    print("Features:")
    print("- Real-time water physics with SPH")
    print("- Interactive floating objects")
    print("- Periodic water drops")
    print("- Optimized for smooth performance")
    print("="*60)
    
    step_count = 0
    start_time = time.time()
    last_fps_time = start_time
    fps_counter = 0
    
    # Simulation parameters
    drop_interval = 25  # Steps between drops
    drop_positions = [
        np.array([0.4, 0.0, 2.0]),
        np.array([-0.4, 0.0, 2.0]),
        np.array([0.0, 0.4, 2.0]),
        np.array([0.0, -0.4, 2.0]),
    ]
    
    try:
        while True:
            # Emit water drops periodically
            if step_count % drop_interval == 0:
                pos_idx = (step_count // drop_interval) % len(drop_positions)
                emitter.emit(
                    pos=drop_positions[pos_idx],
                    direction=np.array([0.0, 0.0, -1.0]),
                    speed=2.0,
                    droplet_shape="circle",
                    droplet_size=0.1,
                )
            
            # Add some randomness occasionally
            if step_count % 100 == 50:
                random_pos = np.array([
                    np.random.uniform(-0.5, 0.5),
                    np.random.uniform(-0.5, 0.5),
                    2.2
                ])
                emitter.emit(
                    pos=random_pos,
                    direction=np.array([0.0, 0.0, -1.0]),
                    speed=1.5,
                    droplet_shape="circle",
                    droplet_size=0.08,
                )
            
            scene.step()
            step_count += 1
            fps_counter += 1
            
            # Print FPS every 2 seconds
            current_time = time.time()
            if current_time - last_fps_time >= 2.0:
                fps = fps_counter / (current_time - last_fps_time)
                elapsed_total = current_time - start_time
                print(f"Step {step_count:5d} | FPS: {fps:5.1f} | Runtime: {elapsed_total:6.1f}s")
                last_fps_time = current_time
                fps_counter = 0
                
    except KeyboardInterrupt:
        total_time = time.time() - start_time
        avg_fps = step_count / total_time
        print(f"\nSimulation stopped by user")
        print(f"Total steps: {step_count}")
        print(f"Total time: {total_time:.1f}s")
        print(f"Average FPS: {avg_fps:.1f}")


def main():
    print("Genesis Interactive Water Simulation")
    print("Press Ctrl+C to stop the simulation")
    
    try:
        run_interactive_simulation()
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure Genesis is properly installed and GPU backend is available")


if __name__ == "__main__":
    main()
