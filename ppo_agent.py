import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal
import numpy as np
import os
import time
from collections import deque
import matplotlib.pyplot as plt


class ActorCritic(nn.Module):
    """Actor-Critic network for PPO."""
    
    def __init__(self, obs_dim, action_dim, hidden_dims=[256, 256], activation='tanh'):
        super(ActorCritic, self).__init__()
        
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        
        # Activation function
        if activation == 'tanh':
            self.activation = nn.Tanh()
        elif activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'elu':
            self.activation = nn.ELU()
        else:
            raise ValueError(f"Unsupported activation: {activation}")
        
        # Actor network
        actor_layers = []
        prev_dim = obs_dim
        for hidden_dim in hidden_dims:
            actor_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self.activation
            ])
            prev_dim = hidden_dim
        actor_layers.append(nn.Linear(prev_dim, action_dim))
        self.actor = nn.Sequential(*actor_layers)
        
        # Critic network
        critic_layers = []
        prev_dim = obs_dim
        for hidden_dim in hidden_dims:
            critic_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self.activation
            ])
            prev_dim = hidden_dim
        critic_layers.append(nn.Linear(prev_dim, 1))
        self.critic = nn.Sequential(*critic_layers)
        
        # Action standard deviation (learnable parameter)
        self.log_std = nn.Parameter(torch.zeros(action_dim))
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.orthogonal_(module.weight, gain=1.0)
            nn.init.constant_(module.bias, 0.0)
    
    def forward(self, obs):
        """Forward pass through both actor and critic."""
        action_mean = self.actor(obs)
        value = self.critic(obs)
        return action_mean, value
    
    def get_action_and_value(self, obs, action=None):
        """Get action, log probability, and value."""
        action_mean, value = self.forward(obs)
        action_std = torch.exp(self.log_std)
        dist = Normal(action_mean, action_std)
        
        if action is None:
            action = dist.sample()
        
        log_prob = dist.log_prob(action).sum(dim=-1)
        entropy = dist.entropy().sum(dim=-1)
        
        return action, log_prob, entropy, value.squeeze(-1)
    
    def get_value(self, obs):
        """Get value estimate."""
        return self.critic(obs).squeeze(-1)


class PPOAgent:
    """Proximal Policy Optimization (PPO) Agent."""
    
    def __init__(self, 
                 obs_dim,
                 action_dim,
                 lr=3e-4,
                 gamma=0.99,
                 gae_lambda=0.95,
                 clip_ratio=0.2,
                 value_loss_coef=0.5,
                 entropy_coef=0.01,
                 max_grad_norm=0.5,
                 hidden_dims=[256, 256],
                 activation='tanh',
                 device='auto'):
        
        # Set device
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"PPO Agent using device: {self.device}")
        
        # Store hyperparameters
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        self.lr = lr
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        
        # Create actor-critic network
        self.actor_critic = ActorCritic(
            obs_dim=obs_dim,
            action_dim=action_dim,
            hidden_dims=hidden_dims,
            activation=activation
        ).to(self.device)
        
        # Optimizer
        self.optimizer = optim.Adam(self.actor_critic.parameters(), lr=lr)
        
        # Training statistics
        self.training_stats = {
            'episode_rewards': deque(maxlen=100),
            'episode_lengths': deque(maxlen=100),
            'policy_losses': deque(maxlen=100),
            'value_losses': deque(maxlen=100),
            'entropy_losses': deque(maxlen=100),
            'total_losses': deque(maxlen=100),
        }
    
    def get_action(self, obs, deterministic=False):
        """Get action from the policy."""
        with torch.no_grad():
            obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(self.device)
            if deterministic:
                action_mean, _ = self.actor_critic(obs_tensor)
                action = action_mean
            else:
                action, _, _, _ = self.actor_critic.get_action_and_value(obs_tensor)
            return action.cpu().numpy().flatten()
    
    def compute_gae(self, rewards, values, dones, next_value):
        """Compute Generalized Advantage Estimation (GAE)."""
        advantages = torch.zeros_like(rewards)
        last_gae = 0
        
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[t]
                next_val = next_value
            else:
                next_non_terminal = 1.0 - dones[t + 1]
                next_val = values[t + 1]
            
            delta = rewards[t] + self.gamma * next_val * next_non_terminal - values[t]
            advantages[t] = last_gae = delta + self.gamma * self.gae_lambda * next_non_terminal * last_gae
        
        returns = advantages + values
        return advantages, returns

    def update(self, rollout_data, num_epochs=4, batch_size=64):
        """Update the policy using PPO."""
        obs, actions, log_probs, values, rewards, dones, advantages, returns = rollout_data

        # Convert to tensors and ensure proper shapes
        obs = torch.FloatTensor(obs).to(self.device)
        actions = torch.FloatTensor(actions).to(self.device)
        old_log_probs = torch.FloatTensor(log_probs).to(self.device)
        old_values = torch.FloatTensor(values).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)

        # Flatten if needed
        if old_log_probs.dim() > 1:
            old_log_probs = old_log_probs.flatten()
        if advantages.dim() > 1:
            advantages = advantages.flatten()
        if returns.dim() > 1:
            returns = returns.flatten()

        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # Training loop
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        total_loss = 0
        num_updates = 0

        for epoch in range(num_epochs):
            # Create mini-batches
            indices = torch.randperm(len(obs))
            for start in range(0, len(obs), batch_size):
                end = start + batch_size
                batch_indices = indices[start:end]

                batch_obs = obs[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]

                # Debug: print shapes to understand the issue
                # print(f"batch_obs: {batch_obs.shape}, batch_actions: {batch_actions.shape}")
                # print(f"batch_returns: {batch_returns.shape}, batch_advantages: {batch_advantages.shape}")

                # Get current policy outputs
                _, new_log_probs, entropy, new_values = self.actor_critic.get_action_and_value(
                    batch_obs, batch_actions
                )

                # Policy loss (PPO clipped objective)
                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()

                # Value loss
                # Ensure shapes match
                if new_values.dim() > 1:
                    new_values = new_values.squeeze()
                if batch_returns.dim() > 1:
                    batch_returns = batch_returns.squeeze()
                value_loss = F.mse_loss(new_values, batch_returns)

                # Entropy loss (for exploration)
                entropy_loss = -entropy.mean()

                # Total loss
                loss = policy_loss + self.value_loss_coef * value_loss + self.entropy_coef * entropy_loss

                # Backward pass
                self.optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.actor_critic.parameters(), self.max_grad_norm)
                self.optimizer.step()

                # Track losses
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
                total_loss += loss.item()
                num_updates += 1

        # Store training statistics
        self.training_stats['policy_losses'].append(total_policy_loss / num_updates)
        self.training_stats['value_losses'].append(total_value_loss / num_updates)
        self.training_stats['entropy_losses'].append(total_entropy_loss / num_updates)
        self.training_stats['total_losses'].append(total_loss / num_updates)

        return {
            'policy_loss': total_policy_loss / num_updates,
            'value_loss': total_value_loss / num_updates,
            'entropy_loss': total_entropy_loss / num_updates,
            'total_loss': total_loss / num_updates,
        }

    def save(self, filepath):
        """Save the model."""
        torch.save({
            'actor_critic_state_dict': self.actor_critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_stats': dict(self.training_stats),
            'hyperparameters': {
                'obs_dim': self.obs_dim,
                'action_dim': self.action_dim,
                'lr': self.lr,
                'gamma': self.gamma,
                'gae_lambda': self.gae_lambda,
                'clip_ratio': self.clip_ratio,
                'value_loss_coef': self.value_loss_coef,
                'entropy_coef': self.entropy_coef,
                'max_grad_norm': self.max_grad_norm,
            }
        }, filepath)
        print(f"Model saved to {filepath}")

    def load(self, filepath):
        """Load the model."""
        checkpoint = torch.load(filepath, map_location=self.device, weights_only=False)
        self.actor_critic.load_state_dict(checkpoint['actor_critic_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'training_stats' in checkpoint:
            for key, value in checkpoint['training_stats'].items():
                self.training_stats[key] = deque(value, maxlen=100)
        print(f"Model loaded from {filepath}")

    def plot_training_stats(self, save_path=None):
        """Plot training statistics."""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))

        # Episode rewards
        if self.training_stats['episode_rewards']:
            axes[0, 0].plot(self.training_stats['episode_rewards'])
            axes[0, 0].set_title('Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Reward')

        # Policy loss
        if self.training_stats['policy_losses']:
            axes[0, 1].plot(self.training_stats['policy_losses'])
            axes[0, 1].set_title('Policy Loss')
            axes[0, 1].set_xlabel('Update')
            axes[0, 1].set_ylabel('Loss')

        # Value loss
        if self.training_stats['value_losses']:
            axes[1, 0].plot(self.training_stats['value_losses'])
            axes[1, 0].set_title('Value Loss')
            axes[1, 0].set_xlabel('Update')
            axes[1, 0].set_ylabel('Loss')

        # Episode lengths
        if self.training_stats['episode_lengths']:
            axes[1, 1].plot(self.training_stats['episode_lengths'])
            axes[1, 1].set_title('Episode Lengths')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Length')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path)
            print(f"Training plots saved to {save_path}")
        else:
            plt.show()

    def get_stats_summary(self):
        """Get a summary of training statistics."""
        if not self.training_stats['episode_rewards']:
            return "No training statistics available yet."

        recent_rewards = list(self.training_stats['episode_rewards'])[-10:]
        recent_lengths = list(self.training_stats['episode_lengths'])[-10:]

        summary = f"""
Training Statistics Summary:
===========================
Episodes completed: {len(self.training_stats['episode_rewards'])}
Recent average reward (last 10): {np.mean(recent_rewards):.2f} ± {np.std(recent_rewards):.2f}
Recent average length (last 10): {np.mean(recent_lengths):.2f} ± {np.std(recent_lengths):.2f}
Best episode reward: {max(self.training_stats['episode_rewards']):.2f}
"""
        return summary
