#!/usr/bin/env python3
"""PS5 DualSense Controller reader using pygame."""

import threading
import time
import numpy as np

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("Warning: pygame not available. PS5 controller support disabled.")


def _apply_deadzone(value, deadzone=0.1):
    """Apply deadzone to analog stick values."""
    if abs(value) < deadzone:
        return 0.0
    # Scale the remaining range to full range
    if value > 0:
        return (value - deadzone) / (1.0 - deadzone)
    else:
        return (value + deadzone) / (1.0 - deadzone)


class PS5Controller:
    """PS5 DualSense Controller reader using pygame."""
    
    def __init__(
        self,
        vel_scale_x=1.0,
        vel_scale_y=1.0,
        vel_scale_rot=1.0,
        deadzone=0.1,
    ):
        if not PYGAME_AVAILABLE:
            raise ImportError("pygame is required for PS5 controller support")
            
        self._vel_scale_x = vel_scale_x
        self._vel_scale_y = vel_scale_y
        self._vel_scale_rot = vel_scale_rot
        self._deadzone = deadzone
        
        # Command values
        self.vx = 0.0  # Forward/backward
        self.vy = 0.0  # Left/right strafe
        self.wz = 0.0  # Rotation
        
        # Controller state
        self.is_running = True
        self.controller = None
        self.connected = False
        
        # Initialize pygame
        pygame.init()
        pygame.joystick.init()
        
        # Start the controller reading thread
        self.read_thread = threading.Thread(target=self._controller_loop, daemon=True)
        self.read_thread.start()
    
    def _find_ps5_controller(self):
        """Find and connect to a PS5 controller."""
        pygame.joystick.quit()
        pygame.joystick.init()
        
        joystick_count = pygame.joystick.get_count()
        print(f"Found {joystick_count} joystick(s)")
        
        for i in range(joystick_count):
            joystick = pygame.joystick.Joystick(i)
            joystick.init()
            name = joystick.get_name().lower()
            print(f"Joystick {i}: {joystick.get_name()}")
            
            # Check for PS5 controller names
            if any(ps5_name in name for ps5_name in [
                'dualsense', 'ps5', 'playstation 5', 'wireless controller'
            ]):
                print(f"✅ Found PS5 controller: {joystick.get_name()}")
                return joystick
            
            # If no PS5 controller found, use the first available controller
            if i == 0:
                print(f"⚠️  No PS5 controller detected, using: {joystick.get_name()}")
                return joystick
        
        return None
    
    def _controller_loop(self):
        """Main controller reading loop."""
        while self.is_running:
            try:
                if not self.connected:
                    self.controller = self._find_ps5_controller()
                    if self.controller:
                        self.connected = True
                        print(f"🎮 Controller connected: {self.controller.get_name()}")
                    else:
                        print("❌ No controller found. Retrying in 2 seconds...")
                        time.sleep(2)
                        continue
                
                # Process pygame events to update joystick state
                pygame.event.pump()
                
                if self.controller and self.connected:
                    self._update_commands()
                
                time.sleep(0.01)  # 100Hz update rate
                
            except Exception as e:
                print(f"Controller error: {e}")
                self.connected = False
                if self.controller:
                    self.controller.quit()
                time.sleep(1)
    
    def _update_commands(self):
        """Update movement commands from controller input."""
        try:
            # Left stick: movement (X=strafe, Y=forward/back)
            left_x = self.controller.get_axis(0)  # Left stick X (strafe)
            left_y = -self.controller.get_axis(1)  # Left stick Y (forward/back, inverted)
            
            # Right stick: rotation
            right_x = self.controller.get_axis(2)  # Right stick X (rotation)
            
            # Apply deadzone and scaling
            self.vx = _apply_deadzone(left_y, self._deadzone) * self._vel_scale_x
            self.vy = _apply_deadzone(left_x, self._deadzone) * self._vel_scale_y
            self.wz = _apply_deadzone(right_x, self._deadzone) * self._vel_scale_rot
            
        except Exception as e:
            print(f"Error reading controller input: {e}")
            self.vx = self.vy = self.wz = 0.0
    
    def get_command(self):
        """Get current movement command as [vx, vy, wz]."""
        return np.array([self.vx, self.vy, self.wz], dtype=np.float32)
    
    def is_connected(self):
        """Check if controller is connected."""
        return self.connected
    
    def stop(self):
        """Stop the controller reader."""
        self.is_running = False
        if self.controller:
            self.controller.quit()
        pygame.quit()
    
    def get_button_state(self, button_id):
        """Get the state of a specific button (0=released, 1=pressed)."""
        if self.controller and self.connected:
            try:
                return self.controller.get_button(button_id)
            except:
                return 0
        return 0
    
    def print_controller_info(self):
        """Print detailed controller information."""
        if self.controller and self.connected:
            print(f"\n🎮 Controller Info:")
            print(f"   Name: {self.controller.get_name()}")
            print(f"   Axes: {self.controller.get_numaxes()}")
            print(f"   Buttons: {self.controller.get_numbuttons()}")
            print(f"   Hats: {self.controller.get_numhats()}")


# Test function
if __name__ == "__main__":
    print("🎮 PS5 Controller Test")
    print("Connect your PS5 controller and press buttons/move sticks")
    print("Press Ctrl+C to exit")
    
    try:
        controller = PS5Controller(
            vel_scale_x=1.0,
            vel_scale_y=1.0, 
            vel_scale_rot=1.0
        )
        
        # Wait for controller connection
        while not controller.is_connected():
            print("Waiting for controller...")
            time.sleep(1)
        
        controller.print_controller_info()
        
        # Main test loop
        while True:
            cmd = controller.get_command()
            print(f"Command: vx={cmd[0]:6.3f}, vy={cmd[1]:6.3f}, wz={cmd[2]:6.3f}", end='\r')
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n👋 Exiting...")
    finally:
        if 'controller' in locals():
            controller.stop()
