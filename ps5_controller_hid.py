#!/usr/bin/env python3
"""PS5 DualSense Controller reader using HID API."""

import os
import threading
import time
import numpy as np

# Set up the library path for hidapi on macOS (try ARM64 first, then x86_64)
arm64_path = '/opt/homebrew/lib'
x86_64_path = '/usr/local/Cellar/hidapi/0.15.0/lib'
current_path = os.environ.get('DYLD_LIBRARY_PATH', '')

if os.path.exists(f'{arm64_path}/libhidapi.dylib'):
    os.environ['DYLD_LIBRARY_PATH'] = f'{arm64_path}:{current_path}'
    print(f"Using ARM64 hidapi from: {arm64_path}")
elif os.path.exists(f'{x86_64_path}/libhidapi.dylib'):
    os.environ['DYLD_LIBRARY_PATH'] = f'{x86_64_path}:{current_path}'
    print(f"Using x86_64 hidapi from: {x86_64_path}")
else:
    print("⚠️  No hidapi library found")

try:
    import hid
    HID_AVAILABLE = True
    print("✅ HID library (hidapi) loaded successfully!")
except ImportError as e:
    HID_AVAILABLE = False
    print(f"❌ HID library not available: {e}")


# PS5 DualSense controller identifiers
PS5_VENDOR_ID = 0x054C  # Sony
PS5_PRODUCT_IDS = [0x0CE6, 0x0DF2]  # Different PS5 controller variants


def _apply_deadzone(value, deadzone=0.1):
    """Apply deadzone to analog stick values."""
    if abs(value) < deadzone:
        return 0.0
    # Scale the remaining range to full range
    if value > 0:
        return (value - deadzone) / (1.0 - deadzone)
    else:
        return (value + deadzone) / (1.0 - deadzone)


class PS5ControllerHID:
    """PS5 DualSense Controller reader using HID API."""
    
    def __init__(
        self,
        vel_scale_x=1.0,
        vel_scale_y=1.0,
        vel_scale_rot=1.0,
        deadzone=0.1,
    ):
        if not HID_AVAILABLE:
            raise ImportError("HID library is required for PS5 controller support")
            
        self._vel_scale_x = vel_scale_x
        self._vel_scale_y = vel_scale_y
        self._vel_scale_rot = vel_scale_rot
        self._deadzone = deadzone
        
        # Command values
        self.vx = 0.0  # Forward/backward
        self.vy = 0.0  # Left/right strafe
        self.wz = 0.0  # Rotation
        
        # Controller state
        self.is_running = True
        self.device_handle = None
        self.connected = False
        
        # Start the controller reading thread
        self.read_thread = threading.Thread(target=self._controller_loop, daemon=True)
        self.read_thread.start()

    def _find_ps5_controller(self):
        """Find and connect to a PS5 controller."""
        print("🔍 Searching for PS5 controllers...")

        # Enumerate HID devices
        devices = hid.enumerate()

        ps5_device = None

        for device in devices:
            vendor_id = device['vendor_id']
            product_id = device['product_id']
            manufacturer = device['manufacturer_string'] or 'Unknown'
            product = device['product_string'] or 'Unknown'

            print(f"Found device: VID=0x{vendor_id:04X}, PID=0x{product_id:04X}")
            print(f"  Manufacturer: {manufacturer}")
            print(f"  Product: {product}")

            # Check if this is a PS5 controller
            if vendor_id == PS5_VENDOR_ID and product_id in PS5_PRODUCT_IDS:
                print("🎮 Found PS5 DualSense controller!")
                ps5_device = device
                break
            elif 'dualsense' in product.lower() or 'ps5' in product.lower():
                print("🎮 Found possible PS5 controller!")
                ps5_device = device
                break

        if ps5_device:
            # Try to open the device
            try:
                device_handle = hid.device()
                device_handle.open_path(ps5_device['path'])
                print("✅ Successfully connected to PS5 controller!")

                # Set non-blocking mode
                device_handle.set_nonblocking(True)

                return device_handle
            except Exception as e:
                print(f"❌ Error opening PS5 controller: {e}")

        return None
    
    def _controller_loop(self):
        """Main controller reading loop."""
        while self.is_running:
            try:
                if not self.connected:
                    self.device_handle = self._find_ps5_controller()
                    if self.device_handle:
                        self.connected = True
                        print("🎮 PS5 Controller connected and ready!")
                    else:
                        print("❌ No PS5 controller found. Retrying in 2 seconds...")
                        time.sleep(2)
                        continue
                
                if self.device_handle and self.connected:
                    self._read_controller_data()

                time.sleep(0.01)  # 100Hz update rate

            except Exception as e:
                print(f"Controller error: {e}")
                self.connected = False
                if self.device_handle:
                    self.device_handle.close()
                    self.device_handle = None
                time.sleep(1)

    def _read_controller_data(self):
        """Read and parse controller data."""
        try:
            # Read data from controller (PS5 reports are typically 64 bytes)
            data = self.device_handle.read(64, timeout_ms=10)  # 10ms timeout
            
            if data and len(data) >= 10:
                # Parse PS5 DualSense input report
                # Byte layout for PS5 controller (simplified):
                # 0: Report ID
                # 1: Left stick X (0-255, center ~128)
                # 2: Left stick Y (0-255, center ~128)  
                # 3: Right stick X (0-255, center ~128)
                # 4: Right stick Y (0-255, center ~128)
                # 5-6: Buttons
                # 7: Left trigger (0-255)
                # 8: Right trigger (0-255)
                
                if len(data) >= 9:
                    left_x = (data[1] - 128) / 128.0    # Convert to -1.0 to 1.0
                    left_y = -(data[2] - 128) / 128.0   # Convert and invert Y
                    right_x = (data[3] - 128) / 128.0   # Convert to -1.0 to 1.0
                    
                    # Apply deadzone and scaling
                    self.vx = _apply_deadzone(left_y, self._deadzone) * self._vel_scale_x
                    self.vy = _apply_deadzone(left_x, self._deadzone) * self._vel_scale_y
                    self.wz = _apply_deadzone(right_x, self._deadzone) * self._vel_scale_rot
                    
        except Exception as e:
            # Don't spam errors for normal timeouts
            if "timeout" not in str(e).lower():
                print(f"Error reading controller data: {e}")
            self.vx = self.vy = self.wz = 0.0
    
    def get_command(self):
        """Get current movement command as [vx, vy, wz]."""
        return np.array([self.vx, self.vy, self.wz], dtype=np.float32)
    
    def is_connected(self):
        """Check if controller is connected."""
        return self.connected
    
    def stop(self):
        """Stop the controller reader."""
        self.is_running = False
        if self.device_handle:
            self.device_handle.close()

    def print_controller_info(self):
        """Print detailed controller information."""
        if self.device_handle and self.connected:
            try:
                manufacturer = self.device_handle.get_manufacturer_string()
                product = self.device_handle.get_product_string()
                serial = self.device_handle.get_serial_number_string()

                print(f"\n🎮 Controller Info:")
                print(f"   Manufacturer: {manufacturer or 'Unknown'}")
                print(f"   Product: {product or 'Unknown'}")
                print(f"   Serial: {serial or 'Unknown'}")
            except Exception as e:
                print(f"Error getting controller info: {e}")


# Test function
if __name__ == "__main__":
    print("🎮 PS5 Controller HID Test")
    print("Connect your PS5 controller and move the sticks")
    print("Press Ctrl+C to exit")
    
    if not HID_AVAILABLE:
        print("❌ HID library not available. Please install pyhidapi.")
        exit(1)
    
    try:
        controller = PS5ControllerHID(
            vel_scale_x=1.0,
            vel_scale_y=1.0, 
            vel_scale_rot=1.0
        )
        
        # Wait for controller connection
        print("Waiting for controller connection...")
        while not controller.is_connected():
            time.sleep(1)
        
        controller.print_controller_info()
        
        # Main test loop
        print("\n📊 Real-time controller data (move the sticks):")
        while True:
            cmd = controller.get_command()
            print(f"Command: vx={cmd[0]:6.3f}, vy={cmd[1]:6.3f}, wz={cmd[2]:6.3f}", end='\r')
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n👋 Exiting...")
    finally:
        if 'controller' in locals():
            controller.stop()
