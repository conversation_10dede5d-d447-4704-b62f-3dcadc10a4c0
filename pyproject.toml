[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "playground"
version = "0.0.4"
readme = "README.md"
authors = [
    {name = "Google DeepMind", email = "<EMAIL>"},
]
requires-python = ">=3.10"
dynamic = ["description"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Framework :: Robot Framework :: Library",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
]
dependencies = [
    "brax>=0.12.1",
    "etils",
    "flax",
    "jax",
    "lxml",
    "ml_collections",
    "mujoco-mjx>=3.2.7",
    "mujoco>=3.2.7",
    "tqdm",
]
keywords = ["mjx", "mujoco", "sim2real", "reinforcement learning"]

[project.optional-dependencies]
test = [
    "absl-py",
    "dm_control",
    "pytest",
    "pytest-xdist",
]
notebooks = [
    "matplotlib",
    "mediapy",
    "jupyter",
    "wandb",
]
dev = [
    "playground[test]",
    "mypy",
    "pyink",
    "pytype",
    "ruff",
    "pre-commit",
    "pylint",
    "pytest-xdist",
]
all = [
    "playground[dev]",
    "playground[notebooks]",
]

[tool.hatch.build.targets.wheel]
packages = ["mujoco_playground"]

[tool.ruff]
lint.select = [
    "E",  # pycodestyle errors.
    "F",  # Pyflakes rules.
    "PLC",  # Pylint convention warnings.
    "PLE",  # Pylint errors.
    "PLR",  # Pylint refactor recommendations.
    "PLW",  # Pylint warnings.
    "I"  # Import sorting.
]
lint.ignore = [
    "E741", # Ambiguous variable name. (l, O, or I)
    "E501",  # Line too long.
    "PLR2004",  # Magic value used in comparison.
    "PLR0915",  # Too many statements.
    "PLR0913",  # Too many arguments.
    "PLC0414",  # Import alias does not rename variable. (this is used for exporting names)
    "PLC1901",  # Use falsey strings.
    "PLR5501",  # Use `elif` instead of `else if`.
    "PLR0911",  # Too many return statements.
    "PLR0912",  # Too many branches.
    "PLW0603",  # Global statement updates are discouraged.
    "PLW2901"  # For loop variable overwritten.
]
exclude = [
    "mujoco_menagerie/",
]

[tool.mypy]
python_version = "3.12"
ignore_missing_imports = true
warn_unused_configs = true
exclude = ["scripts/", "mujoco_menagerie/"]

[tool.pytest.ini_options]
testpaths = [
    "mujoco_playground/_src/",
]

[tool.isort]
force_single_line = true
force_sort_within_sections = true
lexicographical = true
single_line_exclusions = ["typing"]
order_by_type = false
group_by_package = true
line_length = 120
use_parentheses = true
multi_line_output = 3
include_trailing_comma = true
skip = ["mujoco_menagerie", ".pytype"]
skip_glob = ["**/*.ipynb"]

[tool.pyink]
line-length = 80
unstable = true
pyink-indentation = 2
pyink-use-majority-quotes = true
extend-exclude = '''(
 mujoco_menagerie
 | .ipynb$
)'''

[pytype]
inputs = "."
exclude = [
    "**/*_test.py",
    "**/test_*.py",
    "mujoco_menagerie/**",
    "mujoco_playground/_src/wrapper_torch.py",
    "mujoco_playground/learning/getup_rl_games.py"
]
output = ".pytype"
report_errors = true

[tool.pylint]
ignore-paths = 'mujoco_playground/experimental/**$'

[tool.hatch.build]
include = [
    "mujoco_playground/__init__.py",
    "mujoco_playground/_src/**/*",
    "mujoco_playground/config/**/*",
]
