#!/usr/bin/env python3
"""
Run PPO agent with different environments.

Usage examples:
    # Train on CartPole (if gym available)
    python run_ppo.py --env cartpole --train --episodes 1000
    
    # Train on Genesis Go2 environment
    python run_ppo.py --env go2 --train --episodes 500 --show-viewer
    
    # Test with dummy environment
    python run_ppo.py --env dummy --train --episodes 100
    
    # Evaluate trained model
    python run_ppo.py --env cartpole --eval --model-path ppo_model.pth --episodes 10
"""

import argparse
import os
import time
import numpy as np
import torch
from collections import deque

from ppo_agent import PPOAgent
from simple_env_wrapper import (
    create_simple_cartpole_env, 
    create_genesis_go2_env, 
    create_dummy_env
)


def collect_rollout(env, agent, max_steps=1000):
    """Collect a rollout from the environment."""
    obs_list = []
    actions_list = []
    log_probs_list = []
    values_list = []
    rewards_list = []
    dones_list = []
    
    obs = env.reset()
    total_reward = 0
    steps = 0
    
    for step in range(max_steps):
        # Get action and value from agent
        obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(agent.device)
        with torch.no_grad():
            action, log_prob, _, value = agent.actor_critic.get_action_and_value(obs_tensor)
        
        action_np = action.cpu().numpy().flatten()
        
        # Store data
        obs_list.append(obs.copy())
        actions_list.append(action_np.copy())
        log_probs_list.append(log_prob.cpu().numpy())
        values_list.append(value.cpu().numpy())
        
        # Step environment
        next_obs, reward, done, info = env.step(action_np)
        
        rewards_list.append(reward)
        dones_list.append(done)
        
        total_reward += reward
        steps += 1
        obs = next_obs
        
        if done:
            break
    
    # Get final value for GAE computation
    obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(agent.device)
    with torch.no_grad():
        final_value = agent.actor_critic.get_value(obs_tensor).cpu().numpy()
    
    return {
        'observations': np.array(obs_list),
        'actions': np.array(actions_list),
        'log_probs': np.array(log_probs_list),
        'values': np.array(values_list),
        'rewards': np.array(rewards_list),
        'dones': np.array(dones_list),
        'final_value': final_value,
        'total_reward': total_reward,
        'steps': steps
    }


def train_ppo(env, agent, num_episodes=1000, max_steps_per_episode=1000, 
              update_frequency=10, save_frequency=100, model_save_path='ppo_model.pth'):
    """Train the PPO agent."""
    print(f"Starting PPO training for {num_episodes} episodes...")
    print(f"Environment: obs_dim={env.obs_dim}, action_dim={env.action_dim}")
    print(f"Device: {agent.device}")
    
    episode_rewards = []
    episode_lengths = []
    rollout_buffer = []
    
    start_time = time.time()
    
    for episode in range(num_episodes):
        # Collect rollout
        rollout = collect_rollout(env, agent, max_steps_per_episode)
        rollout_buffer.append(rollout)
        
        episode_rewards.append(rollout['total_reward'])
        episode_lengths.append(rollout['steps'])
        
        # Update agent statistics
        agent.training_stats['episode_rewards'].append(rollout['total_reward'])
        agent.training_stats['episode_lengths'].append(rollout['steps'])
        
        # Update policy
        if (episode + 1) % update_frequency == 0 and len(rollout_buffer) > 0:
            # Process each rollout separately to avoid shape issues
            total_loss_info = {'policy_loss': 0, 'value_loss': 0, 'entropy_loss': 0, 'total_loss': 0}

            for rollout in rollout_buffer:
                # Compute advantages and returns for this rollout
                rewards_tensor = torch.FloatTensor(rollout['rewards'])
                values_tensor = torch.FloatTensor(rollout['values'])
                dones_tensor = torch.FloatTensor(rollout['dones'])
                final_value = float(rollout['final_value'].item() if hasattr(rollout['final_value'], 'item') else rollout['final_value'])

                advantages, returns = agent.compute_gae(
                    rewards_tensor, values_tensor, dones_tensor, final_value
                )

                # Update policy with this rollout
                rollout_data = (
                    rollout['observations'], rollout['actions'], rollout['log_probs'],
                    rollout['values'], rollout['rewards'], rollout['dones'],
                    advantages.numpy(), returns.numpy()
                )

                loss_info = agent.update(rollout_data, num_epochs=2, batch_size=min(32, len(rollout['observations'])))

                # Accumulate loss info
                for key in total_loss_info:
                    total_loss_info[key] += loss_info[key]

            # Average the losses
            for key in total_loss_info:
                total_loss_info[key] /= len(rollout_buffer)

            loss_info = total_loss_info
            rollout_buffer = []  # Clear buffer
        
        # Logging
        if (episode + 1) % 10 == 0:
            recent_rewards = episode_rewards[-10:]
            avg_reward = np.mean(recent_rewards)
            avg_length = np.mean(episode_lengths[-10:])
            elapsed_time = time.time() - start_time
            
            print(f"Episode {episode + 1}/{num_episodes}")
            print(f"  Avg Reward (last 10): {avg_reward:.2f}")
            print(f"  Avg Length (last 10): {avg_length:.1f}")
            print(f"  Time Elapsed: {elapsed_time:.1f}s")
            
            if (episode + 1) % update_frequency == 0:
                print(f"  Policy Loss: {loss_info['policy_loss']:.4f}")
                print(f"  Value Loss: {loss_info['value_loss']:.4f}")
        
        # Save model
        if (episode + 1) % save_frequency == 0:
            agent.save(model_save_path)
    
    # Final save
    agent.save(model_save_path)
    print(f"\nTraining completed! Model saved to {model_save_path}")
    print(agent.get_stats_summary())


def evaluate_ppo(env, agent, num_episodes=10, max_steps_per_episode=1000, render=False):
    """Evaluate the PPO agent."""
    print(f"Evaluating PPO agent for {num_episodes} episodes...")
    
    episode_rewards = []
    episode_lengths = []
    
    for episode in range(num_episodes):
        obs = env.reset()
        total_reward = 0
        steps = 0
        
        for step in range(max_steps_per_episode):
            action = agent.get_action(obs, deterministic=True)
            obs, reward, done, info = env.step(action)
            total_reward += reward
            steps += 1
            
            if render:
                env.render()
                time.sleep(0.01)  # Small delay for visualization
            
            if done:
                break
        
        episode_rewards.append(total_reward)
        episode_lengths.append(steps)
        
        print(f"Episode {episode + 1}: Reward = {total_reward:.2f}, Length = {steps}")
    
    avg_reward = np.mean(episode_rewards)
    std_reward = np.std(episode_rewards)
    avg_length = np.mean(episode_lengths)
    
    print(f"\nEvaluation Results:")
    print(f"Average Reward: {avg_reward:.2f} ± {std_reward:.2f}")
    print(f"Average Length: {avg_length:.1f}")
    print(f"Best Episode: {max(episode_rewards):.2f}")
    print(f"Worst Episode: {min(episode_rewards):.2f}")


def main():
    parser = argparse.ArgumentParser(description='Run PPO agent')
    parser.add_argument('--env', choices=['cartpole', 'go2', 'dummy'], default='dummy',
                        help='Environment to use')
    parser.add_argument('--train', action='store_true', help='Train the agent')
    parser.add_argument('--eval', action='store_true', help='Evaluate the agent')
    parser.add_argument('--episodes', type=int, default=100, help='Number of episodes')
    parser.add_argument('--max-steps', type=int, default=1000, help='Max steps per episode')
    parser.add_argument('--model-path', type=str, default='ppo_model.pth', help='Model save/load path')
    parser.add_argument('--show-viewer', action='store_true', help='Show viewer (for Genesis env)')
    parser.add_argument('--render', action='store_true', help='Render during evaluation')
    parser.add_argument('--device', type=str, default='auto', help='Device to use (auto, cpu, cuda)')
    
    args = parser.parse_args()
    
    # Create environment
    print(f"Creating {args.env} environment...")
    if args.env == 'cartpole':
        env = create_simple_cartpole_env()
    elif args.env == 'go2':
        env = create_genesis_go2_env(num_envs=1, show_viewer=args.show_viewer)
        if env is None:
            print("Genesis environment not available, using dummy environment")
            env = create_dummy_env()
    elif args.env == 'dummy':
        env = create_dummy_env()
    else:
        raise ValueError(f"Unknown environment: {args.env}")
    
    print(f"Environment created: obs_dim={env.obs_dim}, action_dim={env.action_dim}")
    
    # Create agent
    agent = PPOAgent(
        obs_dim=env.obs_dim,
        action_dim=env.action_dim,
        device=args.device
    )
    
    # Load model if evaluating
    if args.eval and os.path.exists(args.model_path):
        agent.load(args.model_path)
    elif args.eval:
        print(f"Model file {args.model_path} not found. Using random policy.")
    
    # Train or evaluate
    if args.train:
        train_ppo(env, agent, num_episodes=args.episodes, 
                 max_steps_per_episode=args.max_steps, model_save_path=args.model_path)
    
    if args.eval:
        evaluate_ppo(env, agent, num_episodes=args.episodes, 
                    max_steps_per_episode=args.max_steps, render=args.render)
    
    # Close environment
    env.close()


if __name__ == '__main__':
    main()
