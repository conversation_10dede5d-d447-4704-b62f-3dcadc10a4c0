#!/usr/bin/env python3
"""
Genesis Water Simulation Launcher
Easy launcher for all water simulation demos
"""

import subprocess
import sys
import os


def print_banner():
    print("=" * 60)
    print("🌊 GENESIS WATER SIMULATION SUITE 🌊")
    print("=" * 60)
    print("Optimized water physics simulations using Genesis engine")
    print("Performance: 20-60+ FPS depending on settings")
    print("=" * 60)


def print_menu():
    print("\nAvailable Simulations:")
    print("1. 🚀 Fast SPH (Recommended) - Optimized real-time water")
    print("2. 💧 Standard SPH - Full-featured water simulation") 
    print("3. 🔄 PBD Liquid - Position-based dynamics")
    print("4. ⛲ Water Emitter - Continuous water drops")
    print("5. 🎮 Interactive Demo - Real-time interactive scene")
    print("6. 📊 Performance Benchmark - Test your hardware")
    print("7. 🎬 Quick Demo - All simulation types")
    print("8. ❓ Help - Show command line options")
    print("0. Exit")


def run_command(cmd):
    """Run a command and handle errors"""
    try:
        print(f"\nRunning: {' '.join(cmd)}")
        print("-" * 40)
        result = subprocess.run(cmd, check=True)
        print("-" * 40)
        print("✅ Completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running simulation: {e}")
        return False
    except FileNotFoundError:
        print("❌ Error: Python3 not found or simulation file missing")
        return False


def get_quality_settings():
    """Get quality settings from user"""
    print("\nQuality Settings:")
    print("1. Performance (fastest)")
    print("2. Balanced (recommended)")
    print("3. Quality (best visuals)")
    
    while True:
        choice = input("Choose quality (1-3, default=2): ").strip()
        if choice == "" or choice == "2":
            return "balanced"
        elif choice == "1":
            return "performance"
        elif choice == "3":
            return "quality"
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")


def get_particle_count():
    """Get particle count from user"""
    print("\nParticle Count:")
    print("1. Low (~27K particles)")
    print("2. Medium (~177K particles)")
    print("3. High (~512K particles)")
    
    while True:
        choice = input("Choose particle count (1-3, default=2): ").strip()
        if choice == "" or choice == "2":
            return "medium"
        elif choice == "1":
            return "low"
        elif choice == "3":
            return "high"
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")


def main():
    print_banner()
    
    # Check if files exist
    required_files = ["water_simulation.py", "interactive_water_demo.py", "benchmark_water.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("Please make sure all simulation files are in the current directory.")
        return
    
    while True:
        print_menu()
        choice = input("\nEnter your choice (0-8): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            break
            
        elif choice == "1":  # Fast SPH
            quality = get_quality_settings()
            particles = get_particle_count()
            cmd = ["python3", "water_simulation.py", "--sim_type", "fast_sph", 
                   "--quality", quality, "--particle_count", particles, "--steps", "500"]
            run_command(cmd)
            
        elif choice == "2":  # Standard SPH
            quality = get_quality_settings()
            particles = get_particle_count()
            cmd = ["python3", "water_simulation.py", "--sim_type", "sph",
                   "--quality", quality, "--particle_count", particles, "--steps", "500"]
            run_command(cmd)
            
        elif choice == "3":  # PBD
            quality = get_quality_settings()
            particles = get_particle_count()
            cmd = ["python3", "water_simulation.py", "--sim_type", "pbd",
                   "--quality", quality, "--particle_count", particles, "--steps", "500"]
            run_command(cmd)
            
        elif choice == "4":  # Emitter
            quality = get_quality_settings()
            particles = get_particle_count()
            cmd = ["python3", "water_simulation.py", "--sim_type", "emitter",
                   "--quality", quality, "--particle_count", particles, "--steps", "400"]
            run_command(cmd)
            
        elif choice == "5":  # Interactive Demo
            print("\n🎮 Starting interactive demo...")
            print("Press Ctrl+C in the simulation window to stop")
            cmd = ["python3", "interactive_water_demo.py"]
            run_command(cmd)
            
        elif choice == "6":  # Benchmark
            print("\n📊 Running performance benchmark...")
            cmd = ["python3", "benchmark_water.py", "--mode", "quick"]
            run_command(cmd)
            
        elif choice == "7":  # Quick Demo
            cmd = ["python3", "water_simulation.py", "--sim_type", "demo"]
            run_command(cmd)
            
        elif choice == "8":  # Help
            print("\n📖 Command Line Usage:")
            print("python3 water_simulation.py --help")
            print("\nExample commands:")
            print("python3 water_simulation.py --sim_type fast_sph --quality performance")
            print("python3 water_simulation.py --sim_type emitter --particle_count low")
            print("python3 benchmark_water.py --mode full")
            print("python3 interactive_water_demo.py")
            
        else:
            print("❌ Invalid choice. Please enter a number from 0-8.")
        
        if choice != "0":
            input("\nPress Enter to continue...")


if __name__ == "__main__":
    main()
