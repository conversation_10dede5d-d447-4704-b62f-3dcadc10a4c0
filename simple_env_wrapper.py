import numpy as np
import torch
import gym
from typing import <PERSON><PERSON>, Dict, Any, Optional


class SimpleEnvWrapper:
    """Simple wrapper for different environment types to work with PPO agent."""
    
    def __init__(self, env, env_type='gym'):
        """
        Initialize the environment wrapper.
        
        Args:
            env: The environment instance
            env_type: Type of environment ('gym', 'genesis', 'brax', 'mujoco_playground')
        """
        self.env = env
        self.env_type = env_type
        self._setup_env_interface()
    
    def _setup_env_interface(self):
        """Setup the environment interface based on env_type."""
        if self.env_type == 'gym':
            # Check if it's a real gym environment or our DummyEnv
            if hasattr(self.env, 'observation_space'):
                self.observation_space = self.env.observation_space
                self.action_space = self.env.action_space
                self.obs_dim = self.observation_space.shape[0]

                # Handle both discrete and continuous action spaces
                if hasattr(self.action_space, 'n'):  # Discrete action space
                    self.action_dim = self.action_space.n
                    self.discrete_actions = True
                else:  # Continuous action space
                    self.action_dim = self.action_space.shape[0]
                    self.discrete_actions = False
            else:
                # Handle DummyEnv case
                self.obs_dim = self.env.obs_dim
                self.action_dim = self.env.action_dim
                self.discrete_actions = False
        
        elif self.env_type == 'genesis':
            # Genesis environments like Go2Env
            self.obs_dim = self.env.num_obs
            self.action_dim = self.env.num_actions
            
        elif self.env_type == 'mujoco_playground':
            # MuJoCo playground environments
            # These typically have obs and action spaces
            if hasattr(self.env, 'observation_space'):
                self.obs_dim = self.env.observation_space.shape[0]
            elif hasattr(self.env, 'obs_size'):
                self.obs_dim = self.env.obs_size
            else:
                raise ValueError("Cannot determine observation dimension")
                
            if hasattr(self.env, 'action_space'):
                self.action_dim = self.env.action_space.shape[0]
            elif hasattr(self.env, 'action_size'):
                self.action_dim = self.env.action_size
            else:
                raise ValueError("Cannot determine action dimension")
        
        else:
            raise ValueError(f"Unsupported environment type: {self.env_type}")
    
    def reset(self) -> np.ndarray:
        """Reset the environment and return initial observation."""
        if self.env_type == 'gym':
            obs = self.env.reset()
            if isinstance(obs, tuple):  # New gym API returns (obs, info)
                obs = obs[0]
            return np.array(obs, dtype=np.float32)
        
        elif self.env_type == 'genesis':
            obs, _ = self.env.reset()
            if torch.is_tensor(obs):
                obs = obs.cpu().numpy()
            # For multi-env, take first environment
            if obs.ndim > 1:
                obs = obs[0]
            return np.array(obs, dtype=np.float32)
        
        elif self.env_type == 'mujoco_playground':
            obs = self.env.reset()
            if torch.is_tensor(obs):
                obs = obs.cpu().numpy()
            if obs.ndim > 1:
                obs = obs[0]
            return np.array(obs, dtype=np.float32)
        
        else:
            raise ValueError(f"Unsupported environment type: {self.env_type}")
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """Step the environment with the given action."""
        if self.env_type == 'gym':
            # Handle discrete action spaces (like CartPole)
            if hasattr(self, 'discrete_actions') and self.discrete_actions:
                # Convert continuous action to discrete
                if isinstance(action, np.ndarray):
                    action = int(np.argmax(action))
                else:
                    action = int(action)

            result = self.env.step(action)
            if len(result) == 5:  # New gym API: obs, reward, terminated, truncated, info
                obs, reward, terminated, truncated, info = result
                done = terminated or truncated
            else:  # Old gym API: obs, reward, done, info
                obs, reward, done, info = result

            return np.array(obs, dtype=np.float32), float(reward), bool(done), info
        
        elif self.env_type == 'genesis':
            # Convert action to tensor if needed
            if not torch.is_tensor(action):
                action = torch.tensor(action, dtype=torch.float32, device=self.env.device)
            
            # For single environment, expand action to match num_envs
            if action.ndim == 1:
                action = action.unsqueeze(0).repeat(self.env.num_envs, 1)
            
            obs, reward, done, info = self.env.step(action)
            
            # Convert to numpy and take first environment
            if torch.is_tensor(obs):
                obs = obs[0].cpu().numpy()
            if torch.is_tensor(reward):
                reward = reward[0].cpu().numpy()
            if torch.is_tensor(done):
                done = done[0].cpu().numpy()
            
            return np.array(obs, dtype=np.float32), float(reward), bool(done), info
        
        elif self.env_type == 'mujoco_playground':
            obs, reward, done, info = self.env.step(action)
            
            # Convert tensors to numpy if needed
            if torch.is_tensor(obs):
                obs = obs.cpu().numpy()
            if torch.is_tensor(reward):
                reward = reward.cpu().numpy()
            if torch.is_tensor(done):
                done = done.cpu().numpy()
            
            # Handle multi-environment case
            if obs.ndim > 1:
                obs = obs[0]
            if np.isscalar(reward):
                reward = float(reward)
            else:
                reward = float(reward[0])
            if np.isscalar(done):
                done = bool(done)
            else:
                done = bool(done[0])
            
            return np.array(obs, dtype=np.float32), reward, done, info
        
        else:
            raise ValueError(f"Unsupported environment type: {self.env_type}")
    
    def render(self, mode='human'):
        """Render the environment."""
        if hasattr(self.env, 'render'):
            return self.env.render(mode=mode)
        else:
            print("Rendering not supported for this environment")
    
    def close(self):
        """Close the environment."""
        if hasattr(self.env, 'close'):
            self.env.close()


def create_simple_cartpole_env():
    """Create a simple CartPole environment for testing."""
    try:
        import gym
        env = gym.make('CartPole-v1')
        return SimpleEnvWrapper(env, env_type='gym')
    except ImportError:
        print("Gym not available, creating dummy environment")
        return DummyEnv()


def create_genesis_go2_env(num_envs=1, show_viewer=False):
    """Create a Genesis Go2 environment."""
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.getcwd(), 'Genesis/examples/locomotion'))
        
        from go2_env import Go2Env
        from go2_train import get_cfgs
        
        env_cfg, obs_cfg, reward_cfg, command_cfg = get_cfgs()
        env = Go2Env(
            num_envs=num_envs,
            env_cfg=env_cfg,
            obs_cfg=obs_cfg,
            reward_cfg=reward_cfg,
            command_cfg=command_cfg,
            show_viewer=show_viewer
        )
        return SimpleEnvWrapper(env, env_type='genesis')
    except ImportError as e:
        print(f"Genesis environment not available: {e}")
        return None


class DummyEnv:
    """Dummy environment for testing when real environments are not available."""
    
    def __init__(self):
        self.obs_dim = 4
        self.action_dim = 2
        self.max_steps = 200
        self.current_step = 0
        self.state = np.random.randn(self.obs_dim)
    
    def reset(self):
        self.current_step = 0
        self.state = np.random.randn(self.obs_dim)
        return self.state.copy()
    
    def step(self, action):
        self.current_step += 1
        self.state += 0.1 * np.random.randn(self.obs_dim)
        reward = -np.sum(action**2) * 0.1  # Simple reward
        done = self.current_step >= self.max_steps
        info = {}
        return self.state.copy(), reward, done, info
    
    def render(self, mode='human'):
        pass
    
    def close(self):
        pass


def create_dummy_env():
    """Create a dummy environment for testing."""
    env = DummyEnv()
    return SimpleEnvWrapper(env, env_type='gym')
