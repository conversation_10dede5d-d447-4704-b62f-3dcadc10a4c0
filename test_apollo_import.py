#!/usr/bin/env python3

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

print("Current working directory:", os.getcwd())
print("Python path:")
for p in sys.path:
    print(f"  {p}")

print("\nTrying to import Apollo...")

try:
    from mujoco_playground._src.locomotion.apollo import constants as apollo_constants
    print("✅ Apollo import successful!")
    print("Apollo XML path:", apollo_constants.FEET_ONLY_FLAT_TERRAIN_XML)
except ImportError as e:
    print(f"❌ Apollo import failed: {e}")
    
    # Try alternative import methods
    print("\nTrying alternative import methods...")
    
    try:
        import mujoco_playground._src.locomotion.apollo.constants as apollo_constants
        print("✅ Alternative import successful!")
    except ImportError as e2:
        print(f"❌ Alternative import failed: {e2}")
        
        # Check if the module exists
        apollo_path = os.path.join(os.getcwd(), "mujoco_playground", "_src", "locomotion", "apollo")
        print(f"\nChecking if Apollo directory exists: {apollo_path}")
        print(f"Exists: {os.path.exists(apollo_path)}")
        
        if os.path.exists(apollo_path):
            print("Contents:")
            for item in os.listdir(apollo_path):
                print(f"  {item}")
