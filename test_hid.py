#!/usr/bin/env python3
"""Test HID library and detect PS5 controllers."""

import os
import sys

# Set up the library path for hidapi on macOS
os.environ['DYLD_LIBRARY_PATH'] = '/usr/local/Cellar/hidapi/0.15.0/lib:' + os.environ.get('DYLD_LIBRARY_PATH', '')

print("Testing HID library...")
print(f"DYLD_LIBRARY_PATH: {os.environ.get('DYLD_LIBRARY_PATH', 'Not set')}")

try:
    # Try different HID packages
    hid = None
    hid_package = None

    try:
        import hid
        hid_package = "hid"
        print("✅ HID library (hid package) imported successfully!")
    except ImportError as e1:
        print(f"⚠️  'hid' package failed: {e1}")
        try:
            import pyhidapi as hid
            hid_package = "pyhidapi"
            print("✅ HID library (pyhidapi package) imported successfully!")
        except ImportError as e2:
            print(f"⚠️  'pyhidapi' package failed: {e2}")
            try:
                import hidapi as hid
                hid_package = "hidapi"
                print("✅ HID library (hidapi package) imported successfully!")
            except ImportError as e3:
                print(f"⚠️  'hidapi' package failed: {e3}")
                raise ImportError("No HID packages could be imported")

    print(f"Using HID package: {hid_package}")
    
    # Enumerate all HID devices
    print("\n🔍 Enumerating HID devices...")
    devices = hid.enumerate()
    print(f"Found {len(devices)} HID devices")
    
    # PS5 DualSense controller identifiers
    PS5_VENDOR_ID = 0x054C  # Sony
    PS5_PRODUCT_IDS = [0x0CE6, 0x0DF2]  # Different PS5 controller variants
    
    ps5_controllers = []
    
    print("\n📋 All HID devices:")
    for i, device in enumerate(devices):
        vendor_id = device['vendor_id']
        product_id = device['product_id']
        manufacturer = device['manufacturer_string'] or 'Unknown'
        product = device['product_string'] or 'Unknown'
        
        print(f"{i+1:2d}. Vendor: 0x{vendor_id:04X}, Product: 0x{product_id:04X}")
        print(f"    Manufacturer: {manufacturer}")
        print(f"    Product: {product}")
        print(f"    Path: {device['path'].decode('utf-8') if device['path'] else 'None'}")
        
        # Check if this is a PS5 controller
        if vendor_id == PS5_VENDOR_ID and product_id in PS5_PRODUCT_IDS:
            ps5_controllers.append(device)
            print("    🎮 *** PS5 CONTROLLER DETECTED! ***")
        elif 'dualsense' in product.lower() or 'ps5' in product.lower():
            ps5_controllers.append(device)
            print("    🎮 *** POSSIBLE PS5 CONTROLLER DETECTED! ***")
        
        print()
    
    if ps5_controllers:
        print(f"🎉 Found {len(ps5_controllers)} PS5 controller(s)!")
        
        # Try to connect to the first PS5 controller
        controller = ps5_controllers[0]
        print(f"\n🔌 Attempting to connect to: {controller['product_string']}")
        
        try:
            device = hid.device()
            device.open(controller['vendor_id'], controller['product_id'])
            
            print("✅ Successfully connected to PS5 controller!")
            print(f"Manufacturer: {device.get_manufacturer_string()}")
            print(f"Product: {device.get_product_string()}")
            print(f"Serial: {device.get_serial_number_string()}")
            
            # Try to read some data
            print("\n📡 Testing data read (5 attempts)...")
            device.set_nonblocking(True)
            
            for i in range(5):
                try:
                    data = device.read(64, timeout_ms=100)
                    if data:
                        print(f"  Read {len(data)} bytes: {' '.join(f'{b:02X}' for b in data[:16])}...")
                    else:
                        print(f"  No data received (attempt {i+1})")
                except Exception as e:
                    print(f"  Read error (attempt {i+1}): {e}")
            
            device.close()
            print("✅ Controller test completed successfully!")
            
        except Exception as e:
            print(f"❌ Failed to connect to controller: {e}")
    
    else:
        print("❌ No PS5 controllers detected")
        print("\n💡 To connect a PS5 controller:")
        print("1. USB: Connect via USB-C cable")
        print("2. Bluetooth: Hold PS + Share buttons for 3 seconds, then pair in System Preferences")
        print("3. Run this script again to detect the controller")

except ImportError as e:
    print(f"❌ Failed to import HID library: {e}")
    print("\n🔧 To fix this:")
    print("1. Install hidapi: brew install hidapi")
    print("2. Install Python hidapi: pip3 install hidapi")
    print("3. Set library path: export DYLD_LIBRARY_PATH=/usr/local/Cellar/hidapi/0.15.0/lib:$DYLD_LIBRARY_PATH")

except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 HID test completed.")
