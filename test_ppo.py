#!/usr/bin/env python3
"""
Test script for the PPO agent to verify everything works correctly.
"""

import numpy as np
import torch
import time
import os

from ppo_agent import PPOAgent, ActorCritic
from simple_env_wrapper import create_dummy_env, DummyEnv


def test_actor_critic_network():
    """Test the Actor-Critic network."""
    print("Testing Actor-Critic network...")
    
    obs_dim = 4
    action_dim = 2
    batch_size = 32
    
    # Create network
    network = ActorCritic(obs_dim, action_dim, hidden_dims=[64, 64])
    
    # Test forward pass
    obs = torch.randn(batch_size, obs_dim)
    action_mean, value = network(obs)
    
    assert action_mean.shape == (batch_size, action_dim), f"Expected {(batch_size, action_dim)}, got {action_mean.shape}"
    assert value.shape == (batch_size, 1), f"Expected {(batch_size, 1)}, got {value.shape}"
    
    # Test get_action_and_value
    action, log_prob, entropy, value = network.get_action_and_value(obs)
    
    assert action.shape == (batch_size, action_dim)
    assert log_prob.shape == (batch_size,)
    assert entropy.shape == (batch_size,)
    assert value.shape == (batch_size,)
    
    print("✓ Actor-Critic network tests passed!")


def test_ppo_agent():
    """Test the PPO agent."""
    print("Testing PPO agent...")
    
    obs_dim = 4
    action_dim = 2
    
    # Create agent
    agent = PPOAgent(obs_dim, action_dim, device='cpu')
    
    # Test get_action
    obs = np.random.randn(obs_dim)
    action = agent.get_action(obs)
    
    assert action.shape == (action_dim,), f"Expected {(action_dim,)}, got {action.shape}"
    
    # Test deterministic action
    action_det = agent.get_action(obs, deterministic=True)
    assert action_det.shape == (action_dim,)
    
    # Test GAE computation
    rewards = torch.tensor([1.0, 0.5, -0.1, 0.8, 0.2])
    values = torch.tensor([0.5, 0.3, 0.1, 0.6, 0.4])
    dones = torch.tensor([0.0, 0.0, 0.0, 0.0, 1.0])
    next_value = 0.0
    
    advantages, returns = agent.compute_gae(rewards, values, dones, next_value)
    
    assert advantages.shape == rewards.shape
    assert returns.shape == rewards.shape
    
    print("✓ PPO agent tests passed!")


def test_environment_wrapper():
    """Test the environment wrapper."""
    print("Testing environment wrapper...")
    
    # Test dummy environment
    env = create_dummy_env()
    
    assert hasattr(env, 'obs_dim')
    assert hasattr(env, 'action_dim')
    assert env.obs_dim > 0
    assert env.action_dim > 0
    
    # Test reset
    obs = env.reset()
    assert obs.shape == (env.obs_dim,)
    assert obs.dtype == np.float32
    
    # Test step
    action = np.random.randn(env.action_dim)
    obs, reward, done, info = env.step(action)
    
    assert obs.shape == (env.obs_dim,)
    assert isinstance(reward, (int, float))
    assert isinstance(done, bool)
    assert isinstance(info, dict)
    
    print("✓ Environment wrapper tests passed!")


def test_training_loop():
    """Test a short training loop."""
    print("Testing training loop...")
    
    # Create environment and agent
    env = create_dummy_env()
    agent = PPOAgent(env.obs_dim, env.action_dim, device='cpu')
    
    # Collect a short rollout
    obs = env.reset()
    rollout_data = {
        'observations': [],
        'actions': [],
        'log_probs': [],
        'values': [],
        'rewards': [],
        'dones': []
    }
    
    total_reward = 0
    for step in range(10):  # Short rollout
        # Get action
        obs_tensor = torch.FloatTensor(obs).unsqueeze(0)
        with torch.no_grad():
            action, log_prob, _, value = agent.actor_critic.get_action_and_value(obs_tensor)
        
        action_np = action.cpu().numpy().flatten()
        
        # Store data
        rollout_data['observations'].append(obs.copy())
        rollout_data['actions'].append(action_np.copy())
        rollout_data['log_probs'].append(log_prob.cpu().numpy())
        rollout_data['values'].append(value.cpu().numpy())
        
        # Step environment
        next_obs, reward, done, info = env.step(action_np)
        
        rollout_data['rewards'].append(reward)
        rollout_data['dones'].append(done)
        
        total_reward += reward
        obs = next_obs
        
        if done:
            obs = env.reset()
    
    # Convert to arrays
    for key in rollout_data:
        rollout_data[key] = np.array(rollout_data[key])
    
    # Compute advantages
    rewards_tensor = torch.FloatTensor(rollout_data['rewards'])
    values_tensor = torch.FloatTensor(rollout_data['values'])
    dones_tensor = torch.FloatTensor(rollout_data['dones'])
    final_value = 0.0
    
    advantages, returns = agent.compute_gae(rewards_tensor, values_tensor, dones_tensor, final_value)
    
    # Test update
    update_data = (
        rollout_data['observations'],
        rollout_data['actions'],
        rollout_data['log_probs'],
        rollout_data['values'],
        rollout_data['rewards'],
        rollout_data['dones'],
        advantages.numpy(),
        returns.numpy()
    )
    
    loss_info = agent.update(update_data, num_epochs=1, batch_size=10)
    
    assert 'policy_loss' in loss_info
    assert 'value_loss' in loss_info
    assert 'entropy_loss' in loss_info
    assert 'total_loss' in loss_info
    
    print(f"✓ Training loop test passed! Total reward: {total_reward:.2f}")


def test_save_load():
    """Test model saving and loading."""
    print("Testing model save/load...")
    
    obs_dim = 4
    action_dim = 2
    
    # Create agent
    agent1 = PPOAgent(obs_dim, action_dim, device='cpu')
    
    # Get initial action
    obs = np.random.randn(obs_dim)
    action1 = agent1.get_action(obs, deterministic=True)
    
    # Save model
    save_path = 'test_model.pth'
    agent1.save(save_path)
    
    # Create new agent and load
    agent2 = PPOAgent(obs_dim, action_dim, device='cpu')
    agent2.load(save_path)
    
    # Get action from loaded model
    action2 = agent2.get_action(obs, deterministic=True)
    
    # Actions should be the same
    np.testing.assert_allclose(action1, action2, rtol=1e-5)
    
    # Clean up
    os.remove(save_path)
    
    print("✓ Save/load tests passed!")


def run_quick_demo():
    """Run a quick demo of the PPO agent."""
    print("\nRunning quick demo...")
    
    # Create environment and agent
    env = create_dummy_env()
    agent = PPOAgent(env.obs_dim, env.action_dim, device='cpu')
    
    print(f"Environment: obs_dim={env.obs_dim}, action_dim={env.action_dim}")
    print(f"Agent device: {agent.device}")
    
    # Run a few episodes
    for episode in range(3):
        obs = env.reset()
        total_reward = 0
        steps = 0
        
        for step in range(50):
            action = agent.get_action(obs)
            obs, reward, done, info = env.step(action)
            total_reward += reward
            steps += 1
            
            if done:
                break
        
        print(f"Episode {episode + 1}: Reward = {total_reward:.2f}, Steps = {steps}")
    
    print("✓ Quick demo completed!")


def main():
    """Run all tests."""
    print("Running PPO Agent Tests")
    print("=" * 50)
    
    try:
        test_actor_critic_network()
        test_ppo_agent()
        test_environment_wrapper()
        test_training_loop()
        test_save_load()
        
        print("\n" + "=" * 50)
        print("All tests passed! 🎉")
        
        run_quick_demo()
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = main()
    if success:
        print("\nYou can now run the PPO agent with:")
        print("python run_ppo.py --env dummy --train --episodes 50")
    else:
        print("\nPlease fix the errors before running the PPO agent.")
