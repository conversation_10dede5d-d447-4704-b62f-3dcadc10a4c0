import argparse
import numpy as np
import genesis as gs


def main():
    parser = argparse.ArgumentParser(description="Genesis Water Simulation Demo")
    parser.add_argument("-v", "--vis", action="store_true", default=True, 
                       help="Show visualization (default: True)")
    parser.add_argument("--sim_type", choices=["sph", "pbd", "emitter"], default="sph",
                       help="Type of water simulation (default: sph)")
    parser.add_argument("--steps", type=int, default=1000,
                       help="Number of simulation steps (default: 1000)")
    args = parser.parse_args()

    ########################## init ##########################
    gs.init(seed=0, precision="32", logging_level="info")

    if args.sim_type == "sph":
        run_sph_simulation(args)
    elif args.sim_type == "pbd":
        run_pbd_simulation(args)
    elif args.sim_type == "emitter":
        run_emitter_simulation(args)


def run_sph_simulation(args):
    """Run SPH (Smoothed Particle Hydrodynamics) water simulation"""
    print("Running SPH Water Simulation...")
    
    ########################## create a scene ##########################
    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=4e-3,
            substeps=10,
        ),
        sph_options=gs.options.SPHOptions(
            lower_bound=(-1.0, -1.0, 0.0),
            upper_bound=(1.0, 1.0, 2.0),
            particle_size=0.01,
        ),
        vis_options=gs.options.VisOptions(
            visualize_sph_boundary=True,
            show_world_frame=True,
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(3.5, -2.5, 2.0),
            camera_lookat=(0.0, 0.0, 0.5),
            camera_fov=40,
            max_FPS=60,
        ),
        show_viewer=args.vis,
    )

    ########################## entities ##########################
    # Ground plane
    plane = scene.add_entity(
        morph=gs.morphs.Plane(),
    )

    # Water pool
    water = scene.add_entity(
        material=gs.materials.SPH.Liquid(
            rho=1000.0,      # density
            mu=0.01,         # viscosity
            gamma=0.02,      # surface tension
            sampler="regular"
        ),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 0.3),
            size=(0.8, 0.8, 0.6),
        ),
        surface=gs.surfaces.Default(
            color=(0.2, 0.6, 1.0, 0.8),
            vis_mode="particle",
        ),
    )

    # Optional: Add a cube to interact with water
    cube = scene.add_entity(
        material=gs.materials.Rigid(needs_coup=True, coup_friction=0.1),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 1.5),
            size=(0.15, 0.15, 0.15),
            euler=(45, 30, 0),
            fixed=False,
        ),
        surface=gs.surfaces.Default(
            color=(0.8, 0.2, 0.2, 1.0),
        ),
    )

    ########################## build ##########################
    scene.build()

    print(f"Running {args.steps} simulation steps...")
    for i in range(args.steps):
        if i % 100 == 0:
            print(f"Step {i}/{args.steps}")
        scene.step()

    print("SPH simulation completed!")


def run_pbd_simulation(args):
    """Run PBD (Position Based Dynamics) water simulation"""
    print("Running PBD Water Simulation...")
    
    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=2e-3,
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(3.5, 1.0, 2.5),
            camera_lookat=(0.0, 0.0, 0.5),
            camera_fov=40,
        ),
        show_viewer=args.vis,
        pbd_options=gs.options.PBDOptions(
            lower_bound=(0.0, 0.0, 0.0),
            upper_bound=(1.0, 1.0, 1.0),
            max_density_solver_iterations=10,
            max_viscosity_solver_iterations=1,
        ),
    )

    ########################## entities ##########################
    liquid = scene.add_entity(
        material=gs.materials.PBD.Liquid(
            rho=1000.0, 
            density_relaxation=1.0, 
            viscosity_relaxation=0.0, 
            sampler="regular"
        ),
        morph=gs.morphs.Box(
            lower=(0.2, 0.1, 0.1), 
            upper=(0.4, 0.3, 0.5)
        ),
        surface=gs.surfaces.Default(
            color=(0.3, 0.7, 1.0, 0.8),
            vis_mode="particle",
        ),
    )
    
    scene.build()

    print(f"Running {args.steps} simulation steps...")
    for i in range(args.steps):
        if i % 100 == 0:
            print(f"Step {i}/{args.steps}")
        scene.step()

    print("PBD simulation completed!")


def run_emitter_simulation(args):
    """Run water emitter simulation with continuous water drops"""
    print("Running Water Emitter Simulation...")
    
    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=4e-3,
            substeps=10,
        ),
        sph_options=gs.options.SPHOptions(
            particle_size=0.02,
            lower_bound=(-1.0, -1.0, 0.0),
            upper_bound=(1.0, 1.0, 3.0),
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(3.5, -2.5, 2.0),
            camera_lookat=(0.0, 0.0, 1.0),
            camera_fov=40,
            max_FPS=60,
        ),
        vis_options=gs.options.VisOptions(
            visualize_sph_boundary=True,
        ),
        show_viewer=args.vis,
    )

    # Ground plane
    plane = scene.add_entity(gs.morphs.Plane())
    
    # Water container/pool at bottom
    container = scene.add_entity(
        material=gs.materials.SPH.Liquid(sampler="regular"),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 0.1),
            size=(0.6, 0.6, 0.2),
        ),
        surface=gs.surfaces.Default(
            color=(0.2, 0.6, 1.0, 0.7),
            vis_mode="particle",
        ),
    )

    # Water emitter
    emitter = scene.add_emitter(
        material=gs.materials.SPH.Liquid(sampler="regular"),
        max_particles=50000,
        surface=gs.surfaces.Default(
            color=(0.4, 0.8, 1.0, 0.8),
            vis_mode="particle",
        ),
    )
    
    scene.build()

    print(f"Running {args.steps} simulation steps with water emission...")
    for i in range(args.steps):
        if i % 50 == 0:
            print(f"Step {i}/{args.steps}")
            
        # Emit water drops periodically
        if i % 10 == 0:  # Emit every 10 steps
            emitter.emit(
                pos=np.array([0.0, 0.0, 2.5]),
                direction=np.array([0.0, 0.0, -1.0]),
                speed=3.0,
                droplet_shape="circle",
                droplet_size=0.15,
            )
        
        scene.step()

    print("Emitter simulation completed!")


if __name__ == "__main__":
    main()
